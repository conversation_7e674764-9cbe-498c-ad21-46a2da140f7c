<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Notification Preferences') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <form method="POST" action="{{ route('preferences.update') }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Notification Settings -->
                <x-notification.settings-card :preferences="$preferences" />

                <!-- Investment Preferences -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2 text-green-600"></i>
                            {{ __('Investment Preferences') }}
                        </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label for="min_profit" class="block text-sm font-medium text-gray-700">{{ __('Minimum Expected Profit (R)') }}</label>
                                    <input type="number" name="min_profit" id="min_profit" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" value="{{ $preferences->min_profit }}">
                                </div>
                                
                                <div>
                                    <label for="max_investment" class="block text-sm font-medium text-gray-700">{{ __('Maximum Investment Amount (R)') }}</label>
                                    <input type="number" name="max_investment" id="max_investment" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" value="{{ $preferences->max_investment }}">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label for="min_year" class="block text-sm font-medium text-gray-700">{{ __('Minimum Vehicle Year') }}</label>
                                    <input type="number" name="min_year" id="min_year" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" value="{{ $preferences->min_year }}">
                                </div>
                                
                                <div>
                                    <label for="max_year" class="block text-sm font-medium text-gray-700">{{ __('Maximum Vehicle Year') }}</label>
                                    <input type="number" name="max_year" id="max_year" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" value="{{ $preferences->max_year }}">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="preferred_makes" class="block text-sm font-medium text-gray-700">{{ __('Preferred Makes') }}</label>
                                    <select id="preferred_makes" name="preferred_makes[]" multiple class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        @foreach($makes as $make)
                                            <option value="{{ $make }}" {{ is_array($preferences->preferred_makes) && in_array($make, $preferences->preferred_makes) ? 'selected' : '' }}>{{ $make }}</option>
                                        @endforeach
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500">{{ __('Hold Ctrl/Cmd to select multiple') }}</p>
                                </div>
                                
                                <div>
                                    <label for="preferred_models" class="block text-sm font-medium text-gray-700">{{ __('Preferred Models') }}</label>
                                    <select id="preferred_models" name="preferred_models[]" multiple class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        @if(is_array($preferences->preferred_models))
                                            @foreach($preferences->preferred_models as $model)
                                                <option value="{{ $model }}" selected>{{ $model }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500">{{ __('Select makes first to load available models') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end pt-4 border-t border-gray-200">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <i class="fas fa-save mr-2"></i>
                                {{ __('Save Preferences') }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const makesSelect = document.getElementById('preferred_makes');
            const modelsSelect = document.getElementById('preferred_models');
            
            // Function to load models for selected makes
            function loadModels() {
                const selectedMakes = Array.from(makesSelect.selectedOptions).map(option => option.value);
                
                // Clear current models except those already selected
                const selectedModels = Array.from(modelsSelect.selectedOptions).map(option => option.value);
                
                if (selectedMakes.length > 0) {
                    // For each selected make, fetch models
                    selectedMakes.forEach(make => {
                        fetch(`{{ route('preferences.get-models') }}?make=${encodeURIComponent(make)}`)
                            .then(response => response.json())
                            .then(models => {
                                models.forEach(model => {
                                    // Check if option already exists
                                    if (!Array.from(modelsSelect.options).some(option => option.value === model)) {
                                        const option = new Option(model, model, false, selectedModels.includes(model));
                                        modelsSelect.add(option);
                                    }
                                });
                            });
                    });
                }
            }
            
            // Load models when makes selection changes
            makesSelect.addEventListener('change', loadModels);
            
            // Initial load of models for pre-selected makes
            loadModels();
        });
    </script>
    @endpush
</x-app-layout>

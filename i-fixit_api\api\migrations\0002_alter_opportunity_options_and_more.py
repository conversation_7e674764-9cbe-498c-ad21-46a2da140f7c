# Generated by Django 4.2.21 on 2025-05-13 06:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='opportunity',
            options={'ordering': ['-opportunity_score', '-created_at'], 'verbose_name_plural': 'Opportunities'},
        ),
        migrations.AddField(
            model_name='opportunity',
            name='auction_location',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='has_battery',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='has_keys',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='has_spare_wheel',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='lot_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='odometer',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='stock_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='vehicle_code',
            field=models.CharField(choices=[('1', 'Code 1 - New Vehicle'), ('2', 'Code 2 - Used Vehicle'), ('3', 'Code 3 - Rebuilt Vehicle'), ('4', 'Code 4 - Permanently Unfit'), ('0', 'Unknown')], default='0', max_length=1),
        ),
        migrations.AddField(
            model_name='opportunity',
            name='vehicle_starts',
            field=models.BooleanField(default=False),
        ),
    ]

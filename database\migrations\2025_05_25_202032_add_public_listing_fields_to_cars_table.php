<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cars', function (Blueprint $table) {
            // Public listing fields - adding at end of table since 'status' column doesn't exist yet
            $table->boolean('public_listing')->default(false);
            $table->boolean('featured')->default(false);
            $table->integer('listing_priority')->default(0);

            // SEO fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('seo_keywords')->nullable();

            // Public content
            $table->text('public_description')->nullable();
            $table->json('key_features')->nullable();

            // Analytics
            $table->integer('view_count')->default(0);
            $table->integer('inquiry_count')->default(0);

            // Add indexes for performance
            $table->index(['public_listing', 'featured']);
            $table->index(['public_listing', 'listing_priority']);
            $table->index('view_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cars', function (Blueprint $table) {
            $table->dropIndex(['public_listing', 'featured']);
            $table->dropIndex(['public_listing', 'listing_priority']);
            $table->dropIndex(['view_count']);

            $table->dropColumn([
                'public_listing',
                'featured',
                'listing_priority',
                'meta_title',
                'meta_description',
                'seo_keywords',
                'public_description',
                'key_features',
                'view_count',
                'inquiry_count'
            ]);
        });
    }
};

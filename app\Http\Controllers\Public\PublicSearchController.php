<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\SavedSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class PublicSearchController extends Controller
{
    /**
     * Advanced search page
     */
    public function index()
    {
        $filterOptions = $this->getAdvancedFilterOptions();
        
        return view('public.search.index', compact('filterOptions'));
    }

    /**
     * Perform advanced search
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'make' => 'nullable|string|max:50',
            'model' => 'nullable|string|max:50',
            'year_from' => 'nullable|integer|min:1900|max:' . (date('Y') + 1),
            'year_to' => 'nullable|integer|min:1900|max:' . (date('Y') + 1),
            'price_from' => 'nullable|numeric|min:0',
            'price_to' => 'nullable|numeric|min:0',
            'mileage_max' => 'nullable|integer|min:0',
            'fuel_type' => 'nullable|string|in:petrol,diesel,hybrid,electric',
            'transmission' => 'nullable|string|in:manual,automatic,cvt',
            'features' => 'nullable|array',
            'features.*' => 'integer|exists:car_features,id',
            'investment_potential' => 'nullable|boolean',
            'sort' => 'nullable|string|in:priority,price_low,price_high,year_new,year_old,mileage_low,popular,recent',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Redirect to car listings with search parameters
        return redirect()->route('public.cars.index', $request->all());
    }

    /**
     * Save search for notifications
     */
    public function saveSearch(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:100',
            'search_criteria' => 'required|array',
            'notification_frequency' => 'required|in:immediate,daily,weekly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if user already has this search saved
        $existingSearch = SavedSearch::where('user_email', $request->email)
            ->where('search_criteria', $request->search_criteria)
            ->first();

        if ($existingSearch) {
            return response()->json([
                'success' => false,
                'message' => 'You already have this search saved.'
            ], 409);
        }

        // Create saved search
        $savedSearch = SavedSearch::create([
            'user_email' => $request->email,
            'search_criteria' => $request->search_criteria,
            'name' => $request->name ?: 'Saved Search',
            'notification_frequency' => $request->notification_frequency,
        ]);

        // Update results count
        $savedSearch->updateResultsCount();

        return response()->json([
            'success' => true,
            'message' => 'Search saved successfully! You will receive notifications when new cars match your criteria.',
            'saved_search_id' => $savedSearch->id,
            'results_count' => $savedSearch->results_count,
        ]);
    }

    /**
     * Get saved searches for an email
     */
    public function getSavedSearches(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $savedSearches = SavedSearch::byEmail($request->email)
            ->active()
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'saved_searches' => $savedSearches->map(function ($search) {
                return [
                    'id' => $search->id,
                    'name' => $search->name,
                    'readable_criteria' => $search->readable_criteria,
                    'notification_frequency' => $search->notification_frequency,
                    'results_count' => $search->results_count,
                    'created_at' => $search->created_at->format('Y-m-d H:i'),
                ];
            })
        ]);
    }

    /**
     * Delete a saved search
     */
    public function deleteSavedSearch(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $savedSearch = SavedSearch::where('id', $id)
            ->where('user_email', $request->email)
            ->first();

        if (!$savedSearch) {
            return response()->json([
                'success' => false,
                'message' => 'Saved search not found.'
            ], 404);
        }

        $savedSearch->delete();

        return response()->json([
            'success' => true,
            'message' => 'Saved search deleted successfully.'
        ]);
    }

    /**
     * Get search suggestions
     */
    public function suggestions(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'all'); // all, make, model

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = Cache::remember("search_suggestions_{$type}_{$query}", 1800, function () use ($query, $type) {
            $results = [];

            if ($type === 'all' || $type === 'make') {
                $makes = Car::publicListing()
                    ->where('make', 'like', "%{$query}%")
                    ->select('make')
                    ->distinct()
                    ->limit(5)
                    ->pluck('make');

                foreach ($makes as $make) {
                    $results[] = [
                        'text' => $make,
                        'type' => 'make',
                        'value' => $make,
                    ];
                }
            }

            if ($type === 'all' || $type === 'model') {
                $models = Car::publicListing()
                    ->where('model', 'like', "%{$query}%")
                    ->select('model', 'make')
                    ->distinct()
                    ->limit(5)
                    ->get();

                foreach ($models as $model) {
                    $results[] = [
                        'text' => "{$model->make} {$model->model}",
                        'type' => 'model',
                        'value' => $model->model,
                        'make' => $model->make,
                    ];
                }
            }

            return $results;
        });

        return response()->json($suggestions);
    }

    /**
     * Get popular search terms
     */
    public function popularTerms()
    {
        $popularTerms = Cache::remember('popular_search_terms', 7200, function () {
            return [
                'makes' => Car::publicListing()
                    ->selectRaw('make, COUNT(*) as count')
                    ->groupBy('make')
                    ->orderBy('count', 'desc')
                    ->limit(8)
                    ->pluck('make'),
                'models' => Car::publicListing()
                    ->selectRaw('CONCAT(make, " ", model) as full_name, COUNT(*) as count')
                    ->groupBy('make', 'model')
                    ->orderBy('count', 'desc')
                    ->limit(8)
                    ->pluck('full_name'),
                'price_ranges' => [
                    'Under R200,000',
                    'R200,000 - R400,000',
                    'R400,000 - R600,000',
                    'R600,000 - R800,000',
                    'Over R800,000',
                ],
            ];
        });

        return response()->json($popularTerms);
    }

    /**
     * Get advanced filter options
     */
    private function getAdvancedFilterOptions()
    {
        return Cache::remember('advanced_filter_options', 3600, function () {
            return [
                'makes' => Car::publicListing()
                    ->selectRaw('make, COUNT(*) as count')
                    ->groupBy('make')
                    ->orderBy('make')
                    ->get(),
                'fuel_types' => [
                    'petrol' => 'Petrol',
                    'diesel' => 'Diesel',
                    'hybrid' => 'Hybrid',
                    'electric' => 'Electric',
                ],
                'transmissions' => [
                    'manual' => 'Manual',
                    'automatic' => 'Automatic',
                    'cvt' => 'CVT',
                ],
                'year_range' => [
                    'min' => Car::publicListing()->min('year') ?: 2000,
                    'max' => Car::publicListing()->max('year') ?: date('Y'),
                ],
                'price_range' => [
                    'min' => Car::publicListing()->min('purchase_price') ?: 0,
                    'max' => Car::publicListing()->max('purchase_price') ?: 1000000,
                ],
                'mileage_options' => [
                    50000 => 'Under 50,000 km',
                    100000 => 'Under 100,000 km',
                    150000 => 'Under 150,000 km',
                    200000 => 'Under 200,000 km',
                ],
                'sort_options' => [
                    'priority' => 'Recommended',
                    'price_low' => 'Price: Low to High',
                    'price_high' => 'Price: High to Low',
                    'year_new' => 'Year: Newest First',
                    'year_old' => 'Year: Oldest First',
                    'mileage_low' => 'Mileage: Lowest First',
                    'popular' => 'Most Popular',
                    'recent' => 'Recently Added',
                ],
            ];
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('public_inquiries', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['car_id']);

            // Modify the car_id column to be nullable
            $table->foreignId('car_id')->nullable()->change();

            // Re-add the foreign key constraint with nullable support
            $table->foreign('car_id')->references('id')->on('cars')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('public_inquiries', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['car_id']);

            // Make car_id not nullable again
            $table->foreignId('car_id')->change();

            // Re-add the foreign key constraint
            $table->foreign('car_id')->references('id')->on('cars')->onDelete('cascade');
        });
    }
};

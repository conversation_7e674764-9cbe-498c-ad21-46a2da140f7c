<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\CarFeature;
use App\Models\PublicInquiry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PublicHomeController extends Controller
{
    /**
     * Display the public homepage
     */
    public function index()
    {
        // Get featured cars
        $featuredCars = Cache::remember('featured_cars', 3600, function () {
            return Car::publicListing()
                ->featured()
                ->with(['primaryImage', 'carImages', 'features'])
                ->byPriority()
                ->limit(6)
                ->get();
        });

        // Get latest cars
        $latestCars = Cache::remember('latest_cars', 1800, function () {
            return Car::publicListing()
                ->with(['primaryImage', 'carImages'])
                ->orderBy('created_at', 'desc')
                ->limit(8)
                ->get();
        });

        // Get investment opportunities (high ROI potential)
        $investmentOpportunities = Cache::remember('investment_opportunities', 3600, function () {
            return Car::publicListing()
                ->whereNotNull('estimated_market_value')
                ->whereNotNull('purchase_price')
                ->with(['primaryImage', 'carImages'])
                ->get()
                ->filter(function ($car) {
                    return $car->projected_roi_percentage && $car->projected_roi_percentage > 15;
                })
                ->sortByDesc('projected_roi_percentage')
                ->take(4);
        });

        // Get popular car makes
        $popularMakes = Cache::remember('popular_makes', 7200, function () {
            return Car::publicListing()
                ->selectRaw('make, COUNT(*) as count')
                ->groupBy('make')
                ->orderBy('count', 'desc')
                ->limit(8)
                ->get();
        });

        // Get statistics
        $stats = Cache::remember('homepage_stats', 3600, function () {
            return [
                'total_cars' => Car::publicListing()->count(),
                'cars_sold' => Car::where('current_phase', 'sold')->count(),
                'investment_opportunities' => Car::publicListing()
                    ->whereNotNull('estimated_market_value')
                    ->count(),
                'average_roi' => Car::publicListing()
                    ->whereNotNull('estimated_market_value')
                    ->whereNotNull('purchase_price')
                    ->get()
                    ->filter(function ($car) {
                        return $car->projected_roi_percentage !== null;
                    })
                    ->avg('projected_roi_percentage'),
            ];
        });

        // Get recent inquiries count for trust indicators
        $recentInquiries = PublicInquiry::where('created_at', '>=', now()->subDays(7))->count();

        return view('public.home.index', compact(
            'featuredCars',
            'latestCars',
            'investmentOpportunities',
            'popularMakes',
            'stats',
            'recentInquiries'
        ));
    }

    /**
     * Handle search from homepage
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('public.cars.index');
        }

        return redirect()->route('public.cars.index', ['search' => $query]);
    }

    /**
     * Get search suggestions for autocomplete
     */
    public function searchSuggestions(Request $request)
    {
        $query = $request->get('q');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = Cache::remember("search_suggestions_{$query}", 1800, function () use ($query) {
            $cars = Car::publicListing()
                ->search($query)
                ->select('make', 'model', 'year')
                ->distinct()
                ->limit(10)
                ->get();

            $suggestions = [];

            foreach ($cars as $car) {
                $suggestions[] = [
                    'text' => "{$car->year} {$car->make} {$car->model}",
                    'make' => $car->make,
                    'model' => $car->model,
                    'year' => $car->year,
                ];
            }

            // Add make suggestions
            $makes = Car::publicListing()
                ->where('make', 'like', "%{$query}%")
                ->select('make')
                ->distinct()
                ->limit(5)
                ->pluck('make');

            foreach ($makes as $make) {
                $suggestions[] = [
                    'text' => $make,
                    'type' => 'make',
                    'make' => $make,
                ];
            }

            return array_unique($suggestions, SORT_REGULAR);
        });

        return response()->json($suggestions);
    }

    /**
     * Get popular searches
     */
    public function popularSearches()
    {
        $popularSearches = Cache::remember('popular_searches', 7200, function () {
            return [
                'BMW 3 Series',
                'Mercedes-Benz C-Class',
                'Audi A4',
                'Toyota Camry',
                'Honda Civic',
                'Volkswagen Golf',
                'Ford Focus',
                'Nissan Altima',
            ];
        });

        return response()->json($popularSearches);
    }

    /**
     * Get car categories for navigation
     */
    public function getCarCategories()
    {
        $categories = Cache::remember('car_categories', 7200, function () {
            $makes = Car::publicListing()
                ->selectRaw('make, COUNT(*) as count')
                ->groupBy('make')
                ->orderBy('count', 'desc')
                ->get();

            $priceRanges = [
                ['label' => 'Under R200,000', 'min' => 0, 'max' => 200000],
                ['label' => 'R200,000 - R400,000', 'min' => 200000, 'max' => 400000],
                ['label' => 'R400,000 - R600,000', 'min' => 400000, 'max' => 600000],
                ['label' => 'R600,000 - R800,000', 'min' => 600000, 'max' => 800000],
                ['label' => 'Over R800,000', 'min' => 800000, 'max' => null],
            ];

            $yearRanges = [
                ['label' => '2020 and newer', 'min' => 2020, 'max' => null],
                ['label' => '2015 - 2019', 'min' => 2015, 'max' => 2019],
                ['label' => '2010 - 2014', 'min' => 2010, 'max' => 2014],
                ['label' => '2005 - 2009', 'min' => 2005, 'max' => 2009],
                ['label' => 'Before 2005', 'min' => null, 'max' => 2004],
            ];

            return [
                'makes' => $makes,
                'price_ranges' => $priceRanges,
                'year_ranges' => $yearRanges,
            ];
        });

        return response()->json($categories);
    }
}

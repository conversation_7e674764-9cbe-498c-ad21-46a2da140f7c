/**
 * Micro-Interactions Library
 * Subtle animations that enhance user experience
 */
class MicroInteractions {
    constructor() {
        this.init();
    }

    init() {
        this.setupButtonHovers();
        this.setupCardHovers();
        this.setupFormInteractions();
        this.setupScrollAnimations();
        this.setupLoadingStates();
        this.setupClickEffects();
        this.setupParallaxElements();
    }

    setupButtonHovers() {
        const buttons = document.querySelectorAll('button, .btn, [role="button"]');
        
        buttons.forEach(button => {
            if (button.classList.contains('no-micro-interactions')) return;

            button.addEventListener('mouseenter', () => {
                this.animateButtonHover(button, true);
            });

            button.addEventListener('mouseleave', () => {
                this.animateButtonHover(button, false);
            });

            button.addEventListener('mousedown', () => {
                this.animateButtonPress(button);
            });

            button.addEventListener('mouseup', () => {
                this.animateButtonRelease(button);
            });
        });
    }

    animateButtonHover(button, isHovering) {
        const scale = isHovering ? 1.05 : 1;
        const brightness = isHovering ? 1.1 : 1;
        
        button.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
        button.style.transform = `scale(${scale})`;
        button.style.filter = `brightness(${brightness})`;
        
        if (isHovering) {
            button.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.25)';
        } else {
            button.style.boxShadow = '';
        }
    }

    animateButtonPress(button) {
        button.style.transform = 'scale(0.95)';
        button.style.transition = 'all 0.1s ease';
    }

    animateButtonRelease(button) {
        button.style.transform = 'scale(1.05)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 100);
    }

    setupCardHovers() {
        const cards = document.querySelectorAll('.card, .bento-item, [data-card]');
        
        cards.forEach(card => {
            if (card.classList.contains('no-micro-interactions')) return;

            card.addEventListener('mouseenter', () => {
                this.animateCardHover(card, true);
            });

            card.addEventListener('mouseleave', () => {
                this.animateCardHover(card, false);
            });
        });
    }

    animateCardHover(card, isHovering) {
        const translateY = isHovering ? -8 : 0;
        const scale = isHovering ? 1.02 : 1;
        
        card.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        card.style.transform = `translateY(${translateY}px) scale(${scale})`;
        
        if (isHovering) {
            card.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
        } else {
            card.style.boxShadow = '';
        }

        // Animate child elements
        const title = card.querySelector('h1, h2, h3, h4, h5, h6, .title');
        if (title) {
            title.style.transition = 'transform 0.3s ease';
            title.style.transform = isHovering ? 'translateX(5px)' : 'translateX(0)';
        }

        const icon = card.querySelector('i, .icon');
        if (icon) {
            icon.style.transition = 'transform 0.3s ease';
            icon.style.transform = isHovering ? 'scale(1.1) rotate(5deg)' : 'scale(1) rotate(0deg)';
        }
    }

    setupFormInteractions() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                this.animateInputFocus(input, true);
            });

            input.addEventListener('blur', () => {
                this.animateInputFocus(input, false);
            });

            input.addEventListener('input', () => {
                this.animateInputValidation(input);
            });
        });
    }

    animateInputFocus(input, isFocused) {
        const parent = input.closest('.form-group, .input-group') || input.parentElement;
        
        if (isFocused) {
            input.style.transform = 'scale(1.02)';
            input.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
            parent.style.transform = 'translateY(-2px)';
        } else {
            input.style.transform = 'scale(1)';
            input.style.boxShadow = '';
            parent.style.transform = 'translateY(0)';
        }
        
        input.style.transition = 'all 0.2s ease';
        parent.style.transition = 'transform 0.2s ease';
    }

    animateInputValidation(input) {
        const isValid = input.checkValidity();
        const parent = input.closest('.form-group, .input-group') || input.parentElement;
        
        if (input.value.length > 0) {
            if (isValid) {
                input.style.borderColor = '#10b981';
                this.createSuccessIcon(parent);
            } else {
                input.style.borderColor = '#ef4444';
                this.createErrorIcon(parent);
            }
        } else {
            input.style.borderColor = '';
            this.removeValidationIcons(parent);
        }
    }

    createSuccessIcon(parent) {
        this.removeValidationIcons(parent);
        const icon = document.createElement('i');
        icon.className = 'fas fa-check-circle validation-icon success-icon';
        icon.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            animation: fadeInScale 0.3s ease;
        `;
        parent.style.position = 'relative';
        parent.appendChild(icon);
    }

    createErrorIcon(parent) {
        this.removeValidationIcons(parent);
        const icon = document.createElement('i');
        icon.className = 'fas fa-exclamation-circle validation-icon error-icon';
        icon.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #ef4444;
            animation: shake 0.5s ease;
        `;
        parent.style.position = 'relative';
        parent.appendChild(icon);
    }

    removeValidationIcons(parent) {
        const existingIcons = parent.querySelectorAll('.validation-icon');
        existingIcons.forEach(icon => icon.remove());
    }

    setupScrollAnimations() {
        const animatedElements = document.querySelectorAll('[data-scroll-animation]');
        
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateOnScroll(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            animatedElements.forEach(element => {
                observer.observe(element);
            });
        }
    }

    animateOnScroll(element) {
        const animationType = element.dataset.scrollAnimation;
        const delay = parseInt(element.dataset.animationDelay) || 0;
        
        setTimeout(() => {
            switch (animationType) {
                case 'fade-in':
                    this.fadeIn(element);
                    break;
                case 'slide-up':
                    this.slideUp(element);
                    break;
                case 'slide-left':
                    this.slideLeft(element);
                    break;
                case 'slide-right':
                    this.slideRight(element);
                    break;
                case 'scale-in':
                    this.scaleIn(element);
                    break;
                case 'rotate-in':
                    this.rotateIn(element);
                    break;
            }
        }, delay);
    }

    fadeIn(element) {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.6s ease';
        requestAnimationFrame(() => {
            element.style.opacity = '1';
        });
    }

    slideUp(element) {
        element.style.transform = 'translateY(30px)';
        element.style.opacity = '0';
        element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        requestAnimationFrame(() => {
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
        });
    }

    slideLeft(element) {
        element.style.transform = 'translateX(30px)';
        element.style.opacity = '0';
        element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        requestAnimationFrame(() => {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        });
    }

    slideRight(element) {
        element.style.transform = 'translateX(-30px)';
        element.style.opacity = '0';
        element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        requestAnimationFrame(() => {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        });
    }

    scaleIn(element) {
        element.style.transform = 'scale(0.8)';
        element.style.opacity = '0';
        element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        requestAnimationFrame(() => {
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
        });
    }

    rotateIn(element) {
        element.style.transform = 'rotate(-10deg) scale(0.8)';
        element.style.opacity = '0';
        element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        requestAnimationFrame(() => {
            element.style.transform = 'rotate(0deg) scale(1)';
            element.style.opacity = '1';
        });
    }

    setupLoadingStates() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
                if (submitButton) {
                    this.animateLoadingButton(submitButton);
                }
            });
        });
    }

    animateLoadingButton(button) {
        const originalText = button.textContent;
        const originalHTML = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
        button.style.opacity = '0.7';
        
        // Reset after 3 seconds (adjust based on your needs)
        setTimeout(() => {
            button.disabled = false;
            button.innerHTML = originalHTML;
            button.style.opacity = '1';
        }, 3000);
    }

    setupClickEffects() {
        const clickableElements = document.querySelectorAll('[data-click-effect]');
        
        clickableElements.forEach(element => {
            element.addEventListener('click', (e) => {
                this.createRippleEffect(e, element);
            });
        });
    }

    createRippleEffect(event, element) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    setupParallaxElements() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        if (parallaxElements.length > 0) {
            window.addEventListener('scroll', () => {
                this.updateParallax(parallaxElements);
            });
        }
    }

    updateParallax(elements) {
        const scrollTop = window.pageYOffset;
        
        elements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrollTop * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
}

// Add required CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: translateY(-50%) scale(0.5);
        }
        to {
            opacity: 1;
            transform: translateY(-50%) scale(1);
        }
    }
    
    @keyframes shake {
        0%, 100% { transform: translateY(-50%) translateX(0); }
        25% { transform: translateY(-50%) translateX(-5px); }
        75% { transform: translateY(-50%) translateX(5px); }
    }
    
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    new MicroInteractions();
});

// Export for use in other scripts
window.MicroInteractions = MicroInteractions;

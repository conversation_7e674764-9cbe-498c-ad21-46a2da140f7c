/**
 * Autocomplete Styles
 * Mobile-first approach for all autocomplete components
 */

/* Dropdown container */
.autocomplete-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-height: 200px;
    overflow-y: auto;
    margin-top: 0.25rem;
}

/* Hide dropdown */
.autocomplete-dropdown.hidden {
    display: none;
}

/* List container */
.autocomplete-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* List items */
.autocomplete-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: #374151;
}

.autocomplete-item:hover,
.autocomplete-item:focus {
    background-color: #f3f4f6;
    outline: none;
}

/* No results message */
.autocomplete-no-results {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

/* Count display */
.autocomplete-count {
    font-size: 0.75rem;
    color: #6b7280;
    margin-left: 0.25rem;
}

/* Mobile optimizations */
@media (max-width: 640px) {
    .autocomplete-dropdown {
        max-height: 150px; /* Smaller height on mobile */
    }

    .autocomplete-item {
        padding: 0.75rem 1rem; /* Larger touch targets */
    }
}

/* Scrollbar styling for better UX */
.autocomplete-dropdown::-webkit-scrollbar {
    width: 6px;
}

.autocomplete-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0.375rem;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 0.375rem;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

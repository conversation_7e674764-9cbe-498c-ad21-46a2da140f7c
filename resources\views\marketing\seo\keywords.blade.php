<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Keyword Management') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Keyword Management</h1>
                        <p class="mt-2 text-gray-600">Track and manage keywords for AutoLux SEO performance</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('marketing.seo.index') }}" 
                           class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to SEO Dashboard
                        </a>
                        <button onclick="openAddKeywordModal()" 
                                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Add Keyword
                        </button>
                    </div>
                </div>
            </div>

            <!-- Keywords Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-search text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Keywords</p>
                            <p class="text-2xl font-bold text-gray-900">{{ count($keywords) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-trophy text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Top 10 Rankings</p>
                            <p class="text-2xl font-bold text-gray-900">{{ collect($keywords)->where('ranking', '<=', 10)->count() }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg. Position</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format(collect($keywords)->avg('ranking'), 1) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-percentage text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg. Density</p>
                            <p class="text-2xl font-bold text-gray-900">{{ collect($keywords)->avg('density') }}%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Keywords Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Keyword Performance</h3>
                        <div class="flex space-x-2">
                            <select class="px-3 py-1 border border-gray-300 rounded text-sm">
                                <option>All Pages</option>
                                <option>Homepage</option>
                                <option>Car Listings</option>
                                <option>Car Details</option>
                            </select>
                            <select class="px-3 py-1 border border-gray-300 rounded text-sm">
                                <option>All Rankings</option>
                                <option>Top 10</option>
                                <option>Top 20</option>
                                <option>Needs Work</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Keyword
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Page
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ranking
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Density
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Performance
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($keywords as $keyword)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $keyword['keyword'] }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ ucfirst($keyword['page']) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900">#{{ $keyword['ranking'] }}</span>
                                        @if($keyword['ranking'] <= 3)
                                            <i class="fas fa-trophy text-yellow-500 ml-2"></i>
                                        @elseif($keyword['ranking'] <= 10)
                                            <i class="fas fa-medal text-gray-400 ml-2"></i>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $keyword['density'] }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($keyword['ranking'] <= 10)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                                            Excellent
                                        </span>
                                    @elseif($keyword['ranking'] <= 20)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <div class="w-2 h-2 bg-yellow-400 rounded-full mr-1"></div>
                                            Good
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <div class="w-2 h-2 bg-red-400 rounded-full mr-1"></div>
                                            Needs Work
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="editKeyword({{ $keyword['id'] }})" 
                                                class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteKeyword({{ $keyword['id'] }})" 
                                                class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-2 py-1 rounded transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Keyword Research Tips -->
            <div class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Keyword Research Tips
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Research Tools</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Google Keyword Planner</li>
                            <li>• SEMrush or Ahrefs</li>
                            <li>• Google Search Console</li>
                            <li>• Answer The Public</li>
                        </ul>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Keyword Types</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Brand keywords (AutoLux)</li>
                            <li>• Product keywords (luxury cars)</li>
                            <li>• Long-tail keywords</li>
                            <li>• Local keywords (South Africa)</li>
                        </ul>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Optimization</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Target 1-2 primary keywords per page</li>
                            <li>• Use keywords in titles and headers</li>
                            <li>• Maintain natural keyword density</li>
                            <li>• Monitor competitor keywords</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Keyword Modal -->
    <div id="addKeywordModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Keyword</h3>
                    <form action="{{ route('marketing.seo.keywords.store') }}" method="POST">
                        @csrf
                        <div class="space-y-4">
                            <div>
                                <label for="keyword" class="block text-sm font-medium text-gray-700 mb-1">
                                    Keyword
                                </label>
                                <input type="text" id="keyword" name="keyword" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="luxury cars">
                            </div>
                            <div>
                                <label for="page" class="block text-sm font-medium text-gray-700 mb-1">
                                    Target Page
                                </label>
                                <select id="page" name="page" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select page</option>
                                    <option value="homepage">Homepage</option>
                                    <option value="cars-index">Car Listings</option>
                                    <option value="car-detail">Car Details</option>
                                    <option value="search">Search Page</option>
                                    <option value="contact">Contact Page</option>
                                </select>
                            </div>
                            <div>
                                <label for="target_density" class="block text-sm font-medium text-gray-700 mb-1">
                                    Target Density (%)
                                </label>
                                <input type="number" id="target_density" name="target_density" 
                                       min="0" max="10" step="0.1"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="2.5">
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-3">
                            <button type="button" onclick="closeAddKeywordModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                Add Keyword
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openAddKeywordModal() {
            document.getElementById('addKeywordModal').classList.remove('hidden');
        }

        function closeAddKeywordModal() {
            document.getElementById('addKeywordModal').classList.add('hidden');
        }

        function editKeyword(id) {
            // Implement edit functionality
            alert('Edit keyword functionality would be implemented here');
        }

        function deleteKeyword(id) {
            if (confirm('Are you sure you want to delete this keyword?')) {
                // Implement delete functionality
                alert('Delete keyword functionality would be implemented here');
            }
        }
    </script>
</x-app-layout>

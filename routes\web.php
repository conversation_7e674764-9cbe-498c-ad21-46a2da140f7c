<?php

use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\ApiIntegrationController;
use App\Http\Controllers\AuditLogController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DealershipController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TestNotificationController;
use App\Http\Controllers\CarController;
use App\Http\Controllers\CarImageController;
use App\Http\Controllers\DamagedPartController;
use App\Http\Controllers\LaborController;
use App\Http\Controllers\PaintingController;
use App\Http\Controllers\PartController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\ScheduledReportController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserCarController;
use App\Http\Controllers\UserPreferenceController;
use App\Http\Controllers\Public\PublicHomeController;
use App\Http\Controllers\Public\PublicCarController;
use App\Http\Controllers\Public\PublicSearchController;
use App\Http\Controllers\Public\PublicInquiryController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

/*
|--------------------------------------------------------------------------
| Public Routes (No Authentication Required)
|--------------------------------------------------------------------------
*/

// Public Homepage
Route::get('/', [PublicHomeController::class, 'index'])->name('public.home');
Route::post('/search', [PublicHomeController::class, 'search'])->name('public.search');
Route::get('/search/suggestions', [PublicHomeController::class, 'searchSuggestions'])->name('public.search.suggestions');
Route::get('/search/popular', [PublicHomeController::class, 'popularSearches'])->name('public.search.popular');
Route::get('/categories', [PublicHomeController::class, 'getCarCategories'])->name('public.categories');

// Public Car Listings
Route::get('/cars', [PublicCarController::class, 'index'])->name('public.cars.index');
Route::get('/cars/{car:slug}', [PublicCarController::class, 'show'])->name('public.cars.show');



// Advanced Search
Route::prefix('search')->name('public.search.')->group(function () {
    Route::get('/', [PublicSearchController::class, 'index'])->name('index');
    Route::post('/', [PublicSearchController::class, 'search'])->name('perform');
    Route::post('/save', [PublicSearchController::class, 'saveSearch'])->name('save');
    Route::get('/saved', [PublicSearchController::class, 'getSavedSearches'])->name('saved');
    Route::delete('/saved/{id}', [PublicSearchController::class, 'deleteSavedSearch'])->name('saved.delete');
    Route::get('/suggestions', [PublicSearchController::class, 'suggestions'])->name('suggestions');
    Route::get('/popular-terms', [PublicSearchController::class, 'popularTerms'])->name('popular-terms');
});

// Public Inquiries
Route::prefix('inquiries')->name('public.inquiries.')->group(function () {
    Route::post('/', [PublicInquiryController::class, 'store'])->name('store');
    Route::get('/form/{car}', [PublicInquiryController::class, 'form'])->name('form');
    Route::get('/confirmation/{inquiry}', [PublicInquiryController::class, 'confirmation'])->name('confirmation');
    Route::get('/car/{car}/stats', [PublicInquiryController::class, 'getCarInquiryStats'])->name('car.stats');
    Route::get('/types', [PublicInquiryController::class, 'getInquiryTypes'])->name('types');
    Route::get('/form-fields', [PublicInquiryController::class, 'getFormFields'])->name('form-fields');
});

// Public Contact
Route::prefix('contact')->name('public.contact.')->group(function () {
    Route::get('/', [PublicInquiryController::class, 'contact'])->name('index');
    Route::post('/', [PublicInquiryController::class, 'contact'])->name('store');
    Route::get('/confirmation/{inquiry}', [PublicInquiryController::class, 'confirmation'])->name('confirmation');
});

/*
|--------------------------------------------------------------------------
| Admin Routes (Authentication Required)
|--------------------------------------------------------------------------
*/

// Legacy contact routes (keeping for backward compatibility)
Route::get('/contact-legacy', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact-legacy', [ContactController::class, 'store'])->name('contact.store');

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // My Cars routes (user's own cars)
    Route::resource('my-cars', CarController::class)->names([
        'index' => 'cars.index',
        'create' => 'cars.create',
        'store' => 'cars.store',
        'show' => 'cars.show',
        'edit' => 'cars.edit',
        'update' => 'cars.update',
        'destroy' => 'cars.destroy'
    ])->parameters(['my-cars' => 'car']);

    // User Car Posting routes
    Route::resource('user-cars', UserCarController::class);
    Route::delete('user-cars/{car}/images/{image}', [UserCarController::class, 'deleteImage'])->name('user-cars.delete-image');

    // Car Images routes
    Route::get('my-cars/{car}/images/create', [CarImageController::class, 'create'])->name('car_images.create');
    Route::post('my-cars/{car}/images', [CarImageController::class, 'store'])->name('car_images.store');
    Route::delete('my-cars/{car}/images/{image}', [CarImageController::class, 'destroy'])->name('car_images.destroy');
    Route::post('my-cars/{car}/images/migrate', [CarImageController::class, 'migrateImages'])->name('car_images.migrate');

    // Report routes
    Route::resource('reports', ReportController::class);
    Route::get('reports/{report}/export/pdf', [ReportController::class, 'exportPdf'])->name('reports.export.pdf');
    Route::get('reports/{report}/export/excel', [ReportController::class, 'exportExcel'])->name('reports.export.excel');

    // Scheduled Report routes
    Route::resource('scheduled-reports', ScheduledReportController::class);
    Route::put('scheduled-reports/{scheduledReport}/toggle-active', [ScheduledReportController::class, 'toggleActive'])->name('scheduled-reports.toggle-active');

    // Notification routes
    Route::get('notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('notifications/{notification}', [NotificationController::class, 'destroy'])->name('notifications.destroy');
    Route::get('notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::get('notifications/recent', [NotificationController::class, 'getRecent'])->name('notifications.recent');
    Route::get('notifications/stats', [NotificationController::class, 'getStats'])->name('notifications.stats');
    Route::get('notifications/test', [TestNotificationController::class, 'sendTestNotification'])->name('notifications.test');

    // User Preferences routes
    Route::get('preferences', [UserPreferenceController::class, 'edit'])->name('preferences.edit');
    Route::put('preferences', [UserPreferenceController::class, 'update'])->name('preferences.update');
    Route::get('preferences/models', [UserPreferenceController::class, 'getModels'])->name('preferences.get-models');

    // Damaged Parts routes
    Route::get('my-cars/{car}/damaged-parts/create', [DamagedPartController::class, 'create'])->name('damaged_parts.create');
    Route::post('my-cars/{car}/damaged-parts', [DamagedPartController::class, 'store'])->name('damaged_parts.store');
    Route::get('my-cars/{car}/damaged-parts/{damagedPart}/edit', [DamagedPartController::class, 'edit'])->name('damaged_parts.edit');
    Route::put('my-cars/{car}/damaged-parts/{damagedPart}', [DamagedPartController::class, 'update'])->name('damaged_parts.update');
    Route::delete('my-cars/{car}/damaged-parts/{damagedPart}', [DamagedPartController::class, 'destroy'])->name('damaged_parts.destroy');
    Route::delete('my-cars/{car}/damaged-parts/{damagedPart}/images/{image}', [DamagedPartController::class, 'destroyImage'])->name('damaged_part_images.destroy');

    // Supplier routes
    Route::resource('suppliers', SupplierController::class);
    Route::put('suppliers/{supplier}/restore', [SupplierController::class, 'restore'])->name('suppliers.restore');

    // Parts routes
    Route::middleware(['phase.transition'])->group(function () {
        Route::get('my-cars/{car}/parts/create', [PartController::class, 'create'])->name('parts.create');
        Route::post('my-cars/{car}/parts', [PartController::class, 'store'])->name('parts.store');
        Route::get('my-cars/{car}/parts/{part}/edit', [PartController::class, 'edit'])->name('parts.edit');
        Route::put('my-cars/{car}/parts/{part}', [PartController::class, 'update'])->name('parts.update');
        Route::delete('my-cars/{car}/parts/{part}', [PartController::class, 'destroy'])->name('parts.destroy');
    });

    // Labor routes
    Route::get('my-cars/{car}/labor/create', [LaborController::class, 'create'])->name('labor.create');
    Route::post('my-cars/{car}/labor', [LaborController::class, 'store'])->name('labor.store');
    Route::get('my-cars/{car}/labor/{labor}/edit', [LaborController::class, 'edit'])->name('labor.edit');
    Route::put('my-cars/{car}/labor/{labor}', [LaborController::class, 'update'])->name('labor.update');
    Route::delete('my-cars/{car}/labor/{labor}', [LaborController::class, 'destroy'])->name('labor.destroy');

    // Painting routes
    Route::get('my-cars/{car}/painting/create', [PaintingController::class, 'create'])->name('painting.create');
    Route::post('my-cars/{car}/painting', [PaintingController::class, 'store'])->name('painting.store');
    Route::get('my-cars/{car}/painting/{painting}/edit', [PaintingController::class, 'edit'])->name('painting.edit');
    Route::put('my-cars/{car}/painting/{painting}', [PaintingController::class, 'update'])->name('painting.update');
    Route::delete('my-cars/{car}/painting/{painting}', [PaintingController::class, 'destroy'])->name('painting.destroy');

    // Sale routes
    Route::middleware(['phase.transition'])->group(function () {
        Route::get('my-cars/{car}/sale/create', [SaleController::class, 'create'])->name('sales.create');
        Route::post('my-cars/{car}/sale', [SaleController::class, 'store'])->name('sales.store');
        Route::get('my-cars/{car}/sale/{sale}/edit', [SaleController::class, 'edit'])->name('sales.edit');
        Route::put('my-cars/{car}/sale/{sale}', [SaleController::class, 'update'])->name('sales.update');
        Route::delete('my-cars/{car}/sale/{sale}', [SaleController::class, 'destroy'])->name('sales.destroy');
    });

    // Dealership routes
    Route::get('dealership', [DealershipController::class, 'index'])->name('dealership.index');
    Route::middleware(['phase.transition'])->group(function () {
        Route::get('dealership/my-cars/{car}/record-sale', [DealershipController::class, 'recordSale'])->name('dealership.record-sale');
        Route::post('dealership/my-cars/{car}/record-sale', [DealershipController::class, 'storeSale'])->name('dealership.store-sale');
        Route::get('dealership/my-cars/{car}/edit-discount', [DealershipController::class, 'editDiscount'])->name('dealership.edit-discount');
        Route::post('dealership/my-cars/{car}/update-discount', [DealershipController::class, 'updateDiscount'])->name('dealership.update-discount');
    });

    // User management routes (admin only)
    Route::middleware(['admin', 'sensitive:10,1'])->group(function () {
        // Apply rate limiting to sensitive user operations
        Route::post('users', [UserController::class, 'store'])->name('users.store');
        Route::put('users/{user}', [UserController::class, 'update'])->name('users.update');
        Route::delete('users/{user}', [UserController::class, 'destroy'])->name('users.destroy');
        Route::put('users/{user}/soft-delete', [UserController::class, 'softDelete'])->name('users.soft-delete');
        Route::put('users/{user}/restore', [UserController::class, 'restore'])->name('users.restore');

        // Less sensitive user operations
        Route::get('users', [UserController::class, 'index'])->name('users.index');
        Route::get('users/create', [UserController::class, 'create'])->name('users.create');
        Route::get('users/{user}', [UserController::class, 'show'])->name('users.show');
        Route::get('users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');

        // Audit logs routes
        Route::get('audit-logs', [AuditLogController::class, 'index'])->name('audit-logs.index');
        Route::get('audit-logs/{auditLog}', [AuditLogController::class, 'show'])->name('audit-logs.show');

        // Contact messages admin routes
        Route::get('contact-messages', [ContactController::class, 'adminIndex'])->name('contact.admin.index');
        Route::get('contact-messages/{contact}', [ContactController::class, 'adminShow'])->name('contact.admin.show');
        Route::delete('contact-messages/{contact}', [ContactController::class, 'adminDestroy'])->name('contact.admin.destroy');

        // API Integration routes (admin only)
        Route::get('api-integration', [ApiIntegrationController::class, 'index'])->name('api-integration.index');
        Route::post('api-integration/sync-opportunities', [ApiIntegrationController::class, 'syncOpportunities'])->name('api-integration.sync-opportunities');
        Route::post('api-integration/trigger-scraping', [ApiIntegrationController::class, 'triggerScraping'])->name('api-integration.trigger-scraping');
        Route::get('api-integration/market-analysis/{car}', [ApiIntegrationController::class, 'getMarketAnalysis'])->name('api-integration.market-analysis');
    });

    // Superuser-only routes
    Route::middleware(['superuser', 'sensitive:10,1'])->group(function () {
        // Activity logs routes
        Route::get('activity-logs', [ActivityLogController::class, 'index'])->name('activity-logs.index');
        Route::get('activity-logs/{activityLog}', [ActivityLogController::class, 'show'])->name('activity-logs.show');
        Route::delete('activity-logs/clear', [ActivityLogController::class, 'clearAll'])->name('activity-logs.clear');

        // Marketing routes (superuser only)
        Route::prefix('marketing')->name('marketing.')->group(function () {
            // SEO Management
            Route::prefix('seo')->name('seo.')->group(function () {
                Route::get('/', [\App\Http\Controllers\Marketing\SeoController::class, 'index'])->name('index');
                Route::get('/pages', [\App\Http\Controllers\Marketing\SeoController::class, 'pages'])->name('pages');
                Route::get('/pages/{page}/edit', [\App\Http\Controllers\Marketing\SeoController::class, 'editPage'])->name('pages.edit');
                Route::put('/pages/{page}', [\App\Http\Controllers\Marketing\SeoController::class, 'updatePage'])->name('pages.update');
                Route::get('/keywords', [\App\Http\Controllers\Marketing\SeoController::class, 'keywords'])->name('keywords');
                Route::post('/keywords', [\App\Http\Controllers\Marketing\SeoController::class, 'storeKeyword'])->name('keywords.store');
                Route::put('/keywords/{keyword}', [\App\Http\Controllers\Marketing\SeoController::class, 'updateKeyword'])->name('keywords.update');
                Route::delete('/keywords/{keyword}', [\App\Http\Controllers\Marketing\SeoController::class, 'destroyKeyword'])->name('keywords.destroy');
                Route::get('/meta-tags', [\App\Http\Controllers\Marketing\SeoController::class, 'metaTags'])->name('meta-tags');
                Route::post('/meta-tags', [\App\Http\Controllers\Marketing\SeoController::class, 'storeMetaTag'])->name('meta-tags.store');
                Route::put('/meta-tags/{metaTag}', [\App\Http\Controllers\Marketing\SeoController::class, 'updateMetaTag'])->name('meta-tags.update');
                Route::delete('/meta-tags/{metaTag}', [\App\Http\Controllers\Marketing\SeoController::class, 'destroyMetaTag'])->name('meta-tags.destroy');
            });

            // Analytics
            Route::prefix('analytics')->name('analytics.')->group(function () {
                Route::get('/', [\App\Http\Controllers\Marketing\AnalyticsController::class, 'index'])->name('index');
                Route::get('/traffic', [\App\Http\Controllers\Marketing\AnalyticsController::class, 'traffic'])->name('traffic');
                Route::get('/conversions', [\App\Http\Controllers\Marketing\AnalyticsController::class, 'conversions'])->name('conversions');
                Route::get('/seo-performance', [\App\Http\Controllers\Marketing\AnalyticsController::class, 'seoPerformance'])->name('seo-performance');
                Route::get('/user-behavior', [\App\Http\Controllers\Marketing\AnalyticsController::class, 'userBehavior'])->name('user-behavior');
                Route::get('/export/{type}', [\App\Http\Controllers\Marketing\AnalyticsController::class, 'export'])->name('export');
            });

            // Marketing Reports
            Route::prefix('reports')->name('reports.')->group(function () {
                Route::get('/', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'index'])->name('index');
                Route::get('/seo-report', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'seoReport'])->name('seo');
                Route::get('/traffic-report', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'trafficReport'])->name('traffic');
                Route::get('/conversion-report', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'conversionReport'])->name('conversion');
                Route::get('/performance-report', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'performanceReport'])->name('performance');
                Route::post('/generate', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'generate'])->name('generate');
                Route::get('/export/{report}', [\App\Http\Controllers\Marketing\MarketingReportController::class, 'export'])->name('export');
            });
        });
    });
});

require __DIR__.'/auth.php';


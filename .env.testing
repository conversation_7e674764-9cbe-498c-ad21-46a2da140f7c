APP_NAME="I-fixit"
APP_ENV=testing
APP_KEY=base64:4ms9nTLYmjpfhLoWznufdk0+lzwVQzn3uarzkzm/0bU=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=:memory:

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MAIL_MAILER=array
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
MAIL_ADMIN_EMAIL="<EMAIL>"

# API Configuration
API_BASE_URL=http://127.0.0.1:8000

# API Service Account Credentials (Keep these secure!)
API_SERVICE_USERNAME=laravel_service
API_SERVICE_PASSWORD=Laravel2025!Service
API_SERVICE_EMAIL=<EMAIL>

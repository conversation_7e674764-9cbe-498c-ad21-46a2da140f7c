@extends('reports.pdf.layouts.master')

@section('title', $report->title)

@section('content')
<div class="report-header">
    <h1>{{ $report->title }}</h1>
    <p class="report-meta">
        <strong>Report Type:</strong> {{ $reportType->name }}<br>
        <strong>Generated:</strong> {{ $generatedAt }}<br>
        <strong>Generated by:</strong> {{ $report->user->first_name }} {{ $report->user->last_name }}
    </p>
</div>

<div class="report-content">
    @if(isset($data['message']))
        <div class="alert alert-info">
            <p>{{ $data['message'] }}</p>
        </div>
    @endif

    @if(isset($data['total_cars']))
        <div class="summary-section">
            <h2>Summary</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="label">Total Cars:</span>
                    <span class="value">{{ number_format($data['total_cars']) }}</span>
                </div>
                
                @if(isset($data['sold_cars']))
                <div class="summary-item">
                    <span class="label">Sold Cars:</span>
                    <span class="value">{{ number_format($data['sold_cars']) }}</span>
                </div>
                @endif
                
                @if(isset($data['unsold_cars']))
                <div class="summary-item">
                    <span class="label">Unsold Cars:</span>
                    <span class="value">{{ number_format($data['unsold_cars']) }}</span>
                </div>
                @endif
                
                @if(isset($data['total_investment']))
                <div class="summary-item">
                    <span class="label">Total Investment:</span>
                    <span class="value">R{{ number_format($data['total_investment'], 2) }}</span>
                </div>
                @endif
                
                @if(isset($data['total_revenue']))
                <div class="summary-item">
                    <span class="label">Total Revenue:</span>
                    <span class="value">R{{ number_format($data['total_revenue'], 2) }}</span>
                </div>
                @endif
                
                @if(isset($data['potential_profit']))
                <div class="summary-item">
                    <span class="label">Potential Profit:</span>
                    <span class="value {{ $data['potential_profit'] >= 0 ? 'positive' : 'negative' }}">
                        R{{ number_format($data['potential_profit'], 2) }}
                    </span>
                </div>
                @endif
                
                @if(isset($data['roi_percentage']))
                <div class="summary-item">
                    <span class="label">ROI:</span>
                    <span class="value {{ $data['roi_percentage'] >= 0 ? 'positive' : 'negative' }}">
                        {{ number_format($data['roi_percentage'], 1) }}%
                    </span>
                </div>
                @endif
            </div>
        </div>
    @endif

    @if(isset($filters) && !empty($filters))
        <div class="filters-section">
            <h2>Applied Filters</h2>
            <div class="filters-grid">
                @if(isset($filters['date_range']) && $filters['date_range'])
                    <div class="filter-item">
                        <span class="label">Date Range:</span>
                        <span class="value">{{ ucfirst(str_replace('_', ' ', $filters['date_range'])) }}</span>
                    </div>
                @endif
                
                @if(isset($filters['make']) && $filters['make'])
                    <div class="filter-item">
                        <span class="label">Make:</span>
                        <span class="value">{{ $filters['make'] }}</span>
                    </div>
                @endif
                
                @if(isset($filters['model']) && $filters['model'])
                    <div class="filter-item">
                        <span class="label">Model:</span>
                        <span class="value">{{ $filters['model'] }}</span>
                    </div>
                @endif
                
                @if(isset($filters['year']) && $filters['year'])
                    <div class="filter-item">
                        <span class="label">Year:</span>
                        <span class="value">{{ $filters['year'] }}</span>
                    </div>
                @endif
                
                @if(isset($filters['phase']) && $filters['phase'])
                    <div class="filter-item">
                        <span class="label">Phase:</span>
                        <span class="value">{{ ucfirst($filters['phase']) }}</span>
                    </div>
                @endif
            </div>
        </div>
    @endif

    @if(isset($data['cars']) && count($data['cars']) > 0)
        <div class="cars-section">
            <h2>Car Details</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Vehicle</th>
                        <th>Status</th>
                        <th>Purchase Price</th>
                        <th>Investment</th>
                        <th>Value</th>
                        <th>Profit/Loss</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['cars'] as $car)
                        <tr>
                            <td>{{ $car['year'] ?? '' }} {{ $car['make'] ?? '' }} {{ $car['model'] ?? '' }}</td>
                            <td>{{ ucfirst($car['status'] ?? $car['current_phase'] ?? 'Unknown') }}</td>
                            <td>R{{ number_format($car['purchase_price'] ?? 0, 2) }}</td>
                            <td>R{{ number_format($car['total_investment'] ?? 0, 2) }}</td>
                            <td>R{{ number_format($car['value'] ?? $car['estimated_market_value'] ?? 0, 2) }}</td>
                            <td class="{{ ($car['profit'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                                R{{ number_format($car['profit'] ?? 0, 2) }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif

    @if(isset($data['profitability_by_make']) && count($data['profitability_by_make']) > 0)
        <div class="make-analysis-section">
            <h2>Analysis by Make</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Make</th>
                        <th>Count</th>
                        <th>Investment</th>
                        <th>Potential Value</th>
                        <th>Potential Profit</th>
                        <th>ROI %</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['profitability_by_make'] as $make => $makeData)
                        <tr>
                            <td>{{ $make }}</td>
                            <td>{{ $makeData['count'] }}</td>
                            <td>R{{ number_format($makeData['total_investment'], 2) }}</td>
                            <td>R{{ number_format($makeData['potential_value'], 2) }}</td>
                            <td class="{{ $makeData['potential_profit'] >= 0 ? 'positive' : 'negative' }}">
                                R{{ number_format($makeData['potential_profit'], 2) }}
                            </td>
                            <td class="{{ $makeData['roi_percentage'] >= 0 ? 'positive' : 'negative' }}">
                                {{ number_format($makeData['roi_percentage'], 1) }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif

    @if(isset($data['investment_by_make']) && count($data['investment_by_make']) > 0)
        <div class="make-analysis-section">
            <h2>Investment by Make</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Make</th>
                        <th>Count</th>
                        <th>Investment</th>
                        <th>Potential Value</th>
                        <th>Potential Profit</th>
                        <th>ROI %</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['investment_by_make'] as $make => $makeData)
                        <tr>
                            <td>{{ $make }}</td>
                            <td>{{ $makeData['count'] }}</td>
                            <td>R{{ number_format($makeData['total_investment'], 2) }}</td>
                            <td>R{{ number_format($makeData['potential_value'], 2) }}</td>
                            <td class="{{ $makeData['potential_profit'] >= 0 ? 'positive' : 'negative' }}">
                                R{{ number_format($makeData['potential_profit'], 2) }}
                            </td>
                            <td class="{{ $makeData['roi_percentage'] >= 0 ? 'positive' : 'negative' }}">
                                {{ number_format($makeData['roi_percentage'], 1) }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif

    <div class="footer-note">
        <p><em>This report was automatically generated by the I-fixit Car Investment Tracking System.</em></p>
        <p><em>For questions or support, please contact your system administrator.</em></p>
    </div>
</div>
@endsection

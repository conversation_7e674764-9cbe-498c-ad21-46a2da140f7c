<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $report->title }}</title>
    <style>
        body {
            font-family: 'Helvetica', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 12px;
            line-height: 1.5;
        }
        .container {
            width: 100%;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .header h1 {
            color: #2d3748;
            margin-bottom: 5px;
            font-size: 24px;
        }
        .header p {
            color: #718096;
            margin-top: 0;
        }
        .report-info {
            margin-bottom: 20px;
            font-size: 11px;
        }
        .report-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .report-info td {
            padding: 5px;
        }
        .report-info .label {
            font-weight: bold;
            width: 150px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #2d3748;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 5px;
            font-size: 18px;
        }
        table.data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table.data-table th {
            background-color: #f8f9fa;
            text-align: left;
            padding: 8px;
            border-bottom: 2px solid #e2e8f0;
            font-weight: bold;
        }
        table.data-table td {
            padding: 8px;
            border-bottom: 1px solid #e2e8f0;
        }
        table.data-table tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .summary-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .summary-box h3 {
            margin-top: 0;
            color: #2d3748;
            font-size: 16px;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 10px;
            color: #718096;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .text-success {
            color: #38a169;
        }
        .text-danger {
            color: #e53e3e;
        }
        .currency {
            font-family: 'Courier New', monospace;
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ $report->title }}</h1>
            <p>Generated on {{ $report->generated_at->format('F j, Y, g:i a') }}</p>
        </div>

        <div class="report-info">
            <table>
                <tr>
                    <td class="label">Report Type:</td>
                    <td>{{ $report->reportType->name }}</td>
                </tr>
                <tr>
                    <td class="label">Generated By:</td>
                    <td>{{ $report->user->name }}</td>
                </tr>
                @if(isset($report->filters['date_range']))
                <tr>
                    <td class="label">Date Range:</td>
                    <td>
                        @if($report->filters['date_range'] === 'custom')
                            {{ \Carbon\Carbon::parse($report->filters['start_date'])->format('M d, Y') }} - 
                            {{ \Carbon\Carbon::parse($report->filters['end_date'])->format('M d, Y') }}
                        @else
                            {{ ucfirst(str_replace('_', ' ', $report->filters['date_range'])) }}
                        @endif
                    </td>
                </tr>
                @endif
                @if(isset($report->filters['make']) && $report->filters['make'])
                <tr>
                    <td class="label">Make:</td>
                    <td>{{ $report->filters['make'] }}</td>
                </tr>
                @endif
                @if(isset($report->filters['model']) && $report->filters['model'])
                <tr>
                    <td class="label">Model:</td>
                    <td>{{ $report->filters['model'] }}</td>
                </tr>
                @endif
                @if(isset($report->filters['year']) && $report->filters['year'])
                <tr>
                    <td class="label">Year:</td>
                    <td>{{ $report->filters['year'] }}</td>
                </tr>
                @endif
                @if(isset($report->filters['phase']) && $report->filters['phase'])
                <tr>
                    <td class="label">Phase:</td>
                    <td>{{ ucfirst($report->filters['phase']) }}</td>
                </tr>
                @endif
                @if(isset($report->filters['selected_cars']) && count($report->filters['selected_cars']) > 0)
                <tr>
                    <td class="label">Selected Cars:</td>
                    <td>{{ count($report->filters['selected_cars']) }} cars selected</td>
                </tr>
                @endif
                @if(isset($report->filters['selected_user_name']) && $report->filters['selected_user_name'])
                <tr>
                    <td class="label">User:</td>
                    <td>{{ $report->filters['selected_user_name'] }}</td>
                </tr>
                @endif
            </table>
        </div>

        @yield('content')

        <div class="footer">
            <p>I-fixit Car Investment Tracking System &copy; {{ date('Y') }}</p>
            <p>Report ID: {{ $report->id }} | Generated on {{ $report->generated_at->format('F j, Y, g:i a') }}</p>
        </div>
    </div>
</body>
</html>

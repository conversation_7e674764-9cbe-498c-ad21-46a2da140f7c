# I-fixit User Manual

## Introduction

Welcome to I-fixit, your comprehensive car investment tracking system. This manual will guide you through the features and functionality of the I-fixit application, helping you maximize your car investment profits.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Car Management](#car-management)
4. [Cost Tracking](#cost-tracking)
5. [Sales Management](#sales-management)
6. [Reporting](#reporting)
7. [Notifications](#notifications)
8. [User Settings](#user-settings)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- Screen resolution of at least 1280x720

### Accessing the System
1. Open your web browser
2. Navigate to the I-fixit URL provided by your administrator
3. Enter your username and password
4. Click "Login"

### First-Time Setup
1. Change your default password
2. Set up your profile information
3. Configure notification preferences

## Dashboard Overview

The dashboard provides a quick overview of your car investments, including:

- Cars in each phase (bidding, fixing, dealership, sold)
- Recent activity
- Financial summary
- Alerts and notifications
- Quick access to common actions

### Dashboard Widgets

#### Cars by Phase
Shows the number of cars in each phase of the investment cycle with quick links to filtered views.

#### Financial Summary
Displays key financial metrics:
- Total investment
- Estimated market value
- Projected profit/loss
- ROI percentage

#### Recent Activity
Shows the latest actions taken in the system, such as:
- New cars added
- Phase transitions
- Cost entries
- Sales recorded

#### Alerts
Highlights important information requiring attention:
- Cars at dealership for more than 30 days
- Repair costs exceeding budget
- Cars in repair phase for extended periods

## Car Management

### Adding a New Car
1. Click "Add New Car" from the dashboard or car listing page
2. Complete the multi-step form:
   - Basic Information (make, model, year, etc.)
   - Purchase Details (date, price, auction source)
   - Damage Assessment (description, severity, photos)
   - Additional Information (features, notes)
3. Click "Save" to create the car record

### Viewing Car Details
1. Click on a car from the dashboard or car listing page
2. The car detail page shows:
   - Basic information
   - Financial summary
   - Phase history
   - Images
   - Related records (parts, labor, painting, etc.)

### Managing Car Phases
1. From the car detail page, click "Change Phase"
2. Select the new phase
3. Complete any required information for the phase transition
4. Click "Save" to update the car's phase

## Cost Tracking

### Adding Parts
1. Navigate to the car detail page
2. Click "Add Part"
3. Enter part details:
   - Name and description
   - Supplier
   - Quantity and price
   - Purchase and installation dates
4. Click "Save" to add the part

### Recording Labor
1. Navigate to the car detail page
2. Click "Add Labor"
3. Enter labor details:
   - Service type
   - Description
   - Provider information
   - Hours and rate
   - Service and completion dates
4. Click "Save" to add the labor entry

### Recording Painting
1. Navigate to the car detail page
2. Click "Add Painting"
3. Enter painting details:
   - Painting type (full or partial)
   - Areas covered
   - Provider information
   - Costs
   - Start and completion dates
4. Click "Save" to add the painting entry

## Sales Management

### Listing a Car for Sale
1. Transition the car to the "dealership" phase
2. Enter listing details:
   - Listing date
   - Asking price
   - Platform
   - Notes
3. Click "Save" to update the car's status

### Recording a Sale
1. From the car detail page, click "Record Sale"
2. Enter sale details:
   - Sale date
   - Selling price
   - Buyer information
   - Commission and fees
   - Notes
3. Click "Save" to complete the sale

## Reporting

### Generating Reports
1. Navigate to the Reports section
2. Select the report type:
   - Profitability Analysis
   - Repair Cost Analysis
   - Sales Performance
   - Time at Dealership
   - Investment Summary
3. Set filter criteria (date range, car make/model, etc.)
4. Click "Generate Report"

### Exporting Reports
1. Generate the desired report
2. Click "Export" and select the format:
   - PDF
   - Excel
   - CSV
3. Save the file to your computer

## Notifications

### Notification Types
- Phase transitions
- Cost thresholds exceeded
- Time thresholds exceeded
- System alerts

### Managing Notifications
1. Click the notification bell icon
2. View all notifications
3. Click on a notification to view details
4. Mark notifications as read

### Notification Preferences
1. Navigate to User Settings
2. Select "Notification Preferences"
3. Configure which notifications you want to receive
4. Set delivery methods (in-app, email)
5. Click "Save" to update preferences

## User Settings

### Profile Management
1. Click your username in the top-right corner
2. Select "Profile"
3. Update your information:
   - Name
   - Email
   - Phone
   - Password
4. Click "Save" to update your profile

### Two-Factor Authentication
1. Navigate to User Settings
2. Select "Security"
3. Enable Two-Factor Authentication
4. Follow the setup instructions
5. Save your recovery codes in a secure location

## Troubleshooting

### Common Issues

#### Login Problems
- Ensure caps lock is off
- Reset your password if forgotten
- Contact your administrator if account is locked

#### Data Not Saving
- Check your internet connection
- Ensure all required fields are completed
- Try refreshing the page

#### Reports Not Generating
- Check if you have data matching your filter criteria
- Try using fewer filters
- Contact support if the issue persists

### Getting Help
For additional assistance, contact your system administrator or the I-fixit support team.

{"authHost": "http://localhost", "authEndpoint": "/broadcasting/auth", "clients": [{"appId": "33e0b5eef4234e18", "key": "8be93084d228020a714c8011a24869ca"}], "database": "sqlite", "databaseConfig": {"redis": {}, "sqlite": {"databasePath": "/database/laravel-echo-server.sqlite"}}, "devMode": true, "host": null, "port": "6001", "protocol": "http", "socketio": {}, "secureOptions": 67108864, "sslCertPath": "", "sslKeyPath": "", "sslCertChainPath": "", "sslPassphrase": "", "subscribers": {"http": true, "redis": true}, "apiOriginAllow": {"allowCors": true, "allowOrigin": "http://localhost:80", "allowMethods": "GET, POST", "allowHeaders": "Origin, Content-Type, X-Auth-Token, X-Requested-With, Accept, Authorization, X-CSRF-TOKEN, X-Socket-Id"}}
<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\CarFeature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PublicCarController extends Controller
{
    /**
     * Display car listings
     */
    public function index(Request $request)
    {
        $query = Car::publicListing()->with(['primaryImage', 'carImages', 'features']);

        // Apply search filter
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Apply make filter
        if ($request->filled('make')) {
            $query->where('make', $request->make);
        }

        // Apply model filter
        if ($request->filled('model')) {
            $query->where('model', 'like', '%' . $request->model . '%');
        }

        // Apply year filters
        if ($request->filled('year_from')) {
            $query->where('year', '>=', $request->year_from);
        }
        if ($request->filled('year_to')) {
            $query->where('year', '<=', $request->year_to);
        }

        // Apply price filters
        if ($request->filled('price_from')) {
            $query->where('purchase_price', '>=', $request->price_from);
        }
        if ($request->filled('price_to')) {
            $query->where('purchase_price', '<=', $request->price_to);
        }

        // Apply mileage filter
        if ($request->filled('mileage_max')) {
            $query->where('mileage', '<=', $request->mileage_max);
        }

        // Apply fuel type filter
        if ($request->filled('fuel_type')) {
            $query->where('fuel_type', $request->fuel_type);
        }

        // Apply transmission filter
        if ($request->filled('transmission')) {
            $query->where('transmission', $request->transmission);
        }

        // Apply features filter
        if ($request->filled('features')) {
            $featureIds = is_array($request->features) ? $request->features : [$request->features];
            $query->whereHas('features', function ($q) use ($featureIds) {
                $q->whereIn('car_features.id', $featureIds);
            });
        }

        // Apply investment filter
        if ($request->filled('investment_potential')) {
            $query->whereNotNull('estimated_market_value')
                  ->whereNotNull('purchase_price');
        }

        // Apply sorting
        $sortBy = $request->get('sort', 'priority');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('purchase_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('purchase_price', 'desc');
                break;
            case 'year_new':
                $query->orderBy('year', 'desc');
                break;
            case 'year_old':
                $query->orderBy('year', 'asc');
                break;
            case 'mileage_low':
                $query->orderBy('mileage', 'asc');
                break;
            case 'popular':
                $query->orderBy('view_count', 'desc');
                break;
            case 'recent':
                $query->orderBy('created_at', 'desc');
                break;
            default:
                $query->byPriority();
        }

        // Paginate results
        $cars = $query->paginate(12)->withQueryString();

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        // Get applied filters for display
        $appliedFilters = $this->getAppliedFilters($request);

        return view('public.cars.index', compact('cars', 'filterOptions', 'appliedFilters'));
    }

    /**
     * Display individual car details
     */
    public function show(Car $car)
    {
        // Check if car is publicly available
        if (!$car->public_listing || !$car->is_available) {
            abort(404);
        }

        // Increment view count
        $car->incrementViewCount();

        // Load relationships
        $car->load([
            'carImages' => function ($query) {
                $query->ordered();
            },
            'features',
            'publicInquiries' => function ($query) {
                $query->recent()->limit(5);
            }
        ]);

        // Get similar cars
        $similarCars = Cache::remember("similar_cars_{$car->id}", 1800, function () use ($car) {
            return Car::publicListing()
                ->where('id', '!=', $car->id)
                ->whereNotNull('slug')
                ->where('slug', '!=', '')
                ->where(function ($query) use ($car) {
                    $query->where('make', $car->make)
                          ->orWhere('model', $car->model)
                          ->orWhereBetween('purchase_price', [
                              $car->purchase_price * 0.8,
                              $car->purchase_price * 1.2
                          ]);
                })
                ->with(['primaryImage', 'carImages'])
                ->limit(4)
                ->get();
        });

        // Get investment analysis
        $investmentAnalysis = $this->getInvestmentAnalysis($car);

        // SEO meta data
        $metaTitle = $car->meta_title ?: "{$car->display_name} - I-fixit Cars";
        $metaDescription = $car->meta_description ?: $car->public_description;

        return view('public.cars.show', compact(
            'car',
            'similarCars',
            'investmentAnalysis',
            'metaTitle',
            'metaDescription'
        ));
    }

    /**
     * Get filter options for the listing page
     */
    private function getFilterOptions()
    {
        return Cache::remember('car_filter_options', 3600, function () {
            return [
                'makes' => Car::publicListing()
                    ->select('make')
                    ->distinct()
                    ->orderBy('make')
                    ->pluck('make'),
                'fuel_types' => Car::publicListing()
                    ->select('fuel_type')
                    ->distinct()
                    ->whereNotNull('fuel_type')
                    ->orderBy('fuel_type')
                    ->pluck('fuel_type'),
                'transmissions' => Car::publicListing()
                    ->select('transmission')
                    ->distinct()
                    ->whereNotNull('transmission')
                    ->orderBy('transmission')
                    ->pluck('transmission'),
                'features' => CarFeature::active()
                    ->ordered()
                    ->get()
                    ->groupBy('category'),
                'year_range' => [
                    'min' => Car::publicListing()->min('year'),
                    'max' => Car::publicListing()->max('year'),
                ],
                'price_range' => [
                    'min' => Car::publicListing()->min('purchase_price'),
                    'max' => Car::publicListing()->max('purchase_price'),
                ],
                'mileage_range' => [
                    'min' => Car::publicListing()->min('mileage'),
                    'max' => Car::publicListing()->max('mileage'),
                ],
            ];
        });
    }

    /**
     * Get applied filters for display
     */
    private function getAppliedFilters(Request $request)
    {
        $filters = [];

        if ($request->filled('search')) {
            $filters['search'] = $request->search;
        }
        if ($request->filled('make')) {
            $filters['make'] = $request->make;
        }
        if ($request->filled('model')) {
            $filters['model'] = $request->model;
        }
        if ($request->filled('year_from') || $request->filled('year_to')) {
            $yearFrom = $request->year_from ?: 'Any';
            $yearTo = $request->year_to ?: 'Any';
            $filters['year'] = "{$yearFrom} - {$yearTo}";
        }
        if ($request->filled('price_from') || $request->filled('price_to')) {
            $priceFrom = $request->price_from ? 'R' . number_format($request->price_from) : 'Any';
            $priceTo = $request->price_to ? 'R' . number_format($request->price_to) : 'Any';
            $filters['price'] = "{$priceFrom} - {$priceTo}";
        }
        if ($request->filled('fuel_type')) {
            $filters['fuel_type'] = ucfirst($request->fuel_type);
        }
        if ($request->filled('transmission')) {
            $filters['transmission'] = ucfirst($request->transmission);
        }

        return $filters;
    }

    /**
     * Get investment analysis for a car
     */
    private function getInvestmentAnalysis(Car $car)
    {
        if (!$car->estimated_market_value || !$car->purchase_price) {
            return null;
        }

        $totalInvestment = $car->total_investment;
        $projectedValue = $car->estimated_market_value;
        $projectedProfit = $projectedValue - $totalInvestment;
        $projectedRoi = $car->projected_roi_percentage;

        return [
            'total_investment' => $totalInvestment,
            'projected_value' => $projectedValue,
            'projected_profit' => $projectedProfit,
            'projected_roi' => $projectedRoi,
            'investment_score' => $car->investment_score,
            'holding_period' => $car->holding_period_days,
        ];
    }
}

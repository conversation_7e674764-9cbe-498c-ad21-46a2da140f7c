<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Car;
use App\Models\CarImage;
use App\Models\CarFeature;

echo "Updating existing cars for public display...\n";

// Update existing public cars
$cars = Car::where('public_listing', true)->get();
foreach ($cars as $car) {
    $car->update([
        'estimated_market_value' => $car->purchase_price * 1.15,
        'public_description' => 'This premium vehicle offers excellent investment potential with proven reliability and strong resale value. Carefully inspected and maintained to the highest standards.',
        'meta_title' => $car->year . ' ' . $car->make . ' ' . $car->model . ' - Premium Investment Opportunity',
        'meta_description' => 'Excellent condition ' . $car->make . ' ' . $car->model . ' with strong investment potential and proven reliability.',
    ]);

    // Create sample images if they don't exist
    if ($car->carImages()->count() === 0) {
        $imageTypes = ['exterior', 'interior', 'engine'];
        foreach ($imageTypes as $index => $type) {
            CarImage::create([
                'car_id' => $car->id,
                'image_path' => "car_images/sample_{$car->id}_{$type}.jpg",
                'image_type' => $type,
                'sort_order' => $index,
                'alt_text' => "{$car->display_name} {$type} view",
                'is_primary' => $index === 0,
                'caption' => ucfirst($type) . ' view of ' . $car->display_name,
            ]);
        }
        echo "Added images for {$car->display_name}\n";
    }

    // Attach features if none exist
    if ($car->features()->count() === 0) {
        $features = CarFeature::inRandomOrder()->limit(rand(5, 8))->get();
        if ($features->count() > 0) {
            $car->features()->attach($features->pluck('id'));
            echo "Added features for {$car->display_name}\n";
        }
    }
}

echo "Updated " . $cars->count() . " cars\n";

echo "Sample cars updated successfully!\n";
echo "Total public cars: " . Car::where('public_listing', true)->count() . "\n";

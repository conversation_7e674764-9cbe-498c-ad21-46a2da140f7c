@props(['stats'])

<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6 bg-white border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
                <i class="fas fa-bell mr-2 text-indigo-600"></i>
                {{ __('Notifications') }}
            </h3>
            <a href="{{ route('notifications.index') }}" class="text-sm text-indigo-600 hover:text-indigo-900">
                {{ __('View All') }}
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <!-- Total Notifications -->
            <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ $stats['total'] ?? 0 }}</div>
                <div class="text-xs text-gray-600">{{ __('Total') }}</div>
            </div>

            <!-- Unread Notifications -->
            <div class="text-center p-3 bg-red-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ $stats['unread'] ?? 0 }}</div>
                <div class="text-xs text-red-600">{{ __('Unread') }}</div>
            </div>

            <!-- Today's Notifications -->
            <div class="text-center p-3 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ $stats['today'] ?? 0 }}</div>
                <div class="text-xs text-blue-600">{{ __('Today') }}</div>
            </div>

            <!-- This Week's Notifications -->
            <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ $stats['this_week'] ?? 0 }}</div>
                <div class="text-xs text-green-600">{{ __('This Week') }}</div>
            </div>
        </div>

        @if(isset($stats['by_type']) && count($stats['by_type']) > 0)
        <div class="border-t border-gray-200 pt-4">
            <h4 class="text-sm font-medium text-gray-700 mb-3">{{ __('Notifications by Type') }}</h4>
            <div class="space-y-2">
                @foreach($stats['by_type'] as $type => $count)
                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center">
                        @switch($type)
                            @case('repair_phase_alert')
                                <i class="fas fa-wrench mr-2 text-orange-500"></i>
                                <span>{{ __('Repair Phase Alerts') }}</span>
                                @break
                            @case('dealership_phase_alert')
                                <i class="fas fa-store mr-2 text-blue-500"></i>
                                <span>{{ __('Dealership Phase Alerts') }}</span>
                                @break
                            @case('budget_exceeded_alert')
                                <i class="fas fa-exclamation-triangle mr-2 text-red-500"></i>
                                <span>{{ __('Budget Exceeded Alerts') }}</span>
                                @break
                            @case('opportunity_alert')
                                <i class="fas fa-chart-line mr-2 text-green-500"></i>
                                <span>{{ __('Opportunity Alerts') }}</span>
                                @break
                            @case('public_inquiry')
                                <i class="fas fa-envelope mr-2 text-purple-500"></i>
                                <span>{{ __('Public Inquiries') }}</span>
                                @break
                            @case('parts_request')
                                <i class="fas fa-cogs mr-2 text-gray-500"></i>
                                <span>{{ __('Parts Requests') }}</span>
                                @break
                            @case('sale_completion')
                                <i class="fas fa-handshake mr-2 text-green-600"></i>
                                <span>{{ __('Sale Completions') }}</span>
                                @break
                            @case('phase_transition')
                                <i class="fas fa-arrow-right mr-2 text-indigo-500"></i>
                                <span>{{ __('Phase Transitions') }}</span>
                                @break
                            @case('admin_notification')
                                <i class="fas fa-user-shield mr-2 text-red-600"></i>
                                <span>{{ __('Admin Notifications') }}</span>
                                @break
                            @case('welcome')
                                <i class="fas fa-hand-wave mr-2 text-yellow-500"></i>
                                <span>{{ __('Welcome Messages') }}</span>
                                @break
                            @case('monthly_report')
                                <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                                <span>{{ __('Monthly Reports') }}</span>
                                @break
                            @case('system_maintenance')
                                <i class="fas fa-tools mr-2 text-gray-600"></i>
                                <span>{{ __('System Maintenance') }}</span>
                                @break
                            @case('test')
                                <i class="fas fa-bell mr-2 text-gray-400"></i>
                                <span>{{ __('Test Notifications') }}</span>
                                @break
                            @default
                                <i class="fas fa-bell mr-2 text-gray-400"></i>
                                <span>{{ ucfirst(str_replace('_', ' ', $type)) }}</span>
                        @endswitch
                    </div>
                    <span class="font-medium text-gray-900">{{ $count }}</span>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        @if(($stats['unread'] ?? 0) > 0)
        <div class="border-t border-gray-200 pt-4 mt-4">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">{{ __('You have unread notifications') }}</span>
                <form action="{{ route('notifications.mark-all-as-read') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-check mr-1"></i>
                        {{ __('Mark All as Read') }}
                    </button>
                </form>
            </div>
        </div>
        @endif

        <!-- Quick Actions -->
        <div class="border-t border-gray-200 pt-4 mt-4">
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('notifications.test') }}" class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-bell mr-1 text-blue-500"></i>
                    {{ __('Test Notification') }}
                </a>
                
                <a href="{{ route('preferences.edit') }}" class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-cog mr-1 text-gray-500"></i>
                    {{ __('Settings') }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Sidebar -->
<div x-show="sidebarOpen"
     x-transition:enter="transition ease-in-out duration-300 transform"
     x-transition:enter-start="-translate-x-full"
     x-transition:enter-end="translate-x-0"
     x-transition:leave="transition ease-in-out duration-300 transform"
     x-transition:leave-start="translate-x-0"
     x-transition:leave-end="-translate-x-full"
     class="lg:hidden fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200"
     style="display: none;">
    <div class="flex flex-col h-full">
        <!-- Mobile Logo -->
        <div class="flex items-center justify-between flex-shrink-0 px-4 py-4 border-b border-gray-200">
            <a href="{{ route('dashboard') }}" class="flex items-center">
                <div class="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center text-white font-bold mr-2">
                    IF
                </div>
                <span class="text-xl font-bold text-gray-900">I-fixit</span>
            </a>
            <button @click="sidebarOpen = false" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Mobile Navigation - Same as original responsive menu -->
        <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto scrollbar-hide">
            <!-- Dashboard -->
            <a href="{{ route('dashboard') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Dashboard
            </a>

            <!-- My Cars -->
            <a href="{{ route('cars.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('cars.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                My Cars
            </a>

            <!-- Post Car -->
            <a href="{{ route('user-cars.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('user-cars.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Post Car
            </a>

            <!-- Suppliers -->
            <a href="{{ route('suppliers.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('suppliers.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Suppliers
            </a>

            <!-- Dealership -->
            <a href="{{ route('dealership.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dealership.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Dealership
            </a>

            <!-- Reports -->
            <a href="{{ route('reports.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('reports.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Reports
            </a>

            @if(Auth::user()->hasAdminAccess())
            <!-- Admin Section -->
            <div class="border-t border-gray-200 my-4"></div>

            <!-- Users -->
            <a href="{{ route('users.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('users.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                Users
            </a>

            <!-- Contact Messages -->
            <a href="{{ route('contact.admin.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('contact.admin.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Contact Messages
            </a>

            <!-- API Integration -->
            <a href="{{ route('api-integration.index') }}"
               @click="sidebarOpen = false"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('api-integration.*') ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 717.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                </svg>
                API Integration
            </a>
            @endif

            @if(Auth::user()->isSuperuser() && Auth::user()->isActive())
            <!-- Marketing Section -->
            <div x-data="{ open: {{ request()->routeIs('marketing.*') ? 'true' : 'false' }} }">
                <button @click="open = !open"
                        class="group w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.*') ? 'bg-purple-100 text-purple-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                        </svg>
                        MARKETING
                    </div>
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path x-show="!open" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        <path x-show="open" fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
                <div x-show="open" class="ml-6 mt-1 space-y-1">
                    <a href="{{ route('marketing.seo.index') }}"
                       @click="sidebarOpen = false"
                       class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.seo.*') ? 'bg-purple-50 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        SEO Management
                    </a>
                    <a href="{{ route('marketing.analytics.index') }}"
                       @click="sidebarOpen = false"
                       class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.analytics.*') ? 'bg-purple-50 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Analytics
                    </a>
                    <a href="{{ route('marketing.reports.index') }}"
                       @click="sidebarOpen = false"
                       class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.reports.*') ? 'bg-purple-50 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                        </svg>
                        Marketing Reports
                    </a>
                </div>
            </div>
            @endif
        </nav>
    </div>
</div>

<!-- Desktop Sidebar -->
<div class="hidden lg:flex lg:flex-shrink-0">
    <div class="flex flex-col w-64">
        <div class="flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto">
            <!-- Logo -->
            <div class="flex items-center flex-shrink-0 px-4">
                <a href="{{ route('dashboard') }}" class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-car text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-gray-900">I-FIXIT</h1>
                        <p class="text-xs text-gray-500">Car Investment Tracker</p>
                    </div>
                </a>
            </div>

            <!-- Navigation -->
            <nav class="mt-8 flex-1 px-2 space-y-1 overflow-y-auto scrollbar-hide">
                <!-- Dashboard -->
                <a href="{{ route('dashboard') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-tachometer-alt mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('dashboard') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    Dashboard
                </a>

                <!-- Cars -->
                <div x-data="{ open: {{ request()->routeIs('cars.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open"
                            class="group w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('cars.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <div class="flex items-center">
                            <i class="fas fa-car mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('cars.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                            Cars
                        </div>
                        <i class="fas fa-chevron-right transform transition-transform duration-200" :class="{ 'rotate-90': open }"></i>
                    </button>
                    <div x-show="open" x-collapse class="ml-6 mt-1 space-y-1">
                        <a href="{{ route('cars.index') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('cars.index') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-list mr-2 h-4 w-4"></i>
                            All Cars
                        </a>
                        <a href="{{ route('cars.create') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('cars.create') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-plus mr-2 h-4 w-4"></i>
                            Add Car
                        </a>
                        <a href="{{ route('cars.index', ['phase' => 'bidding']) }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                            <i class="fas fa-gavel mr-2 h-4 w-4"></i>
                            Bidding Phase
                        </a>
                        <a href="{{ route('cars.index', ['phase' => 'fixing']) }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                            <i class="fas fa-wrench mr-2 h-4 w-4"></i>
                            Fixing Phase
                        </a>
                        <a href="{{ route('cars.index', ['phase' => 'dealership']) }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                            <i class="fas fa-store mr-2 h-4 w-4"></i>
                            Dealership
                        </a>
                        <a href="{{ route('cars.index', ['phase' => 'sold']) }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                            <i class="fas fa-check-circle mr-2 h-4 w-4"></i>
                            Sold
                        </a>
                    </div>
                </div>

                <!-- Dealership -->
                <a href="{{ route('dealership.index') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dealership.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-store mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('dealership.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    Dealership
                </a>

                <!-- Reports -->
                <div x-data="{ open: {{ request()->routeIs('reports.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open"
                            class="group w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('reports.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <div class="flex items-center">
                            <i class="fas fa-chart-bar mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('reports.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                            Reports
                        </div>
                        <i class="fas fa-chevron-right transform transition-transform duration-200" :class="{ 'rotate-90': open }"></i>
                    </button>
                    <div x-show="open" x-collapse class="ml-6 mt-1 space-y-1">
                        <a href="{{ route('reports.index') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('reports.index') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-chart-line mr-2 h-4 w-4"></i>
                            All Reports
                        </a>
                        <a href="{{ route('scheduled-reports.index') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('scheduled-reports.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-clock mr-2 h-4 w-4"></i>
                            Scheduled Reports
                        </a>
                    </div>
                </div>

                <!-- Suppliers -->
                <a href="{{ route('suppliers.index') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('suppliers.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-truck mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('suppliers.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    Suppliers
                </a>

                <!-- Notifications -->
                <a href="{{ route('notifications.index') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('notifications.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-bell mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('notifications.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    Notifications
                </a>

                @if(Auth::user()->hasAdminAccess())
                <!-- Divider -->
                <div class="border-t border-gray-200 my-4"></div>

                <!-- Admin Section -->
                <div class="px-2 py-2">
                    <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Administration</h3>
                </div>

                <!-- Users -->
                <a href="{{ route('users.index') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('users.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-users-cog mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('users.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    Users
                </a>

                <!-- API Integration -->
                <a href="{{ route('api-integration.index') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('api-integration.*') ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-plug mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('api-integration.*') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    API Integration
                </a>
                @endif

                @if(Auth::user()->isSuperuser() && Auth::user()->isActive())
                <!-- Marketing Section -->
                <div x-data="{ open: {{ request()->routeIs('marketing.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open"
                            class="group w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.*') ? 'bg-purple-100 text-purple-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <div class="flex items-center">
                            <i class="fas fa-chart-pie mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('marketing.*') ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                            MARKETING
                        </div>
                        <i class="fas fa-chevron-right transform transition-transform duration-200" :class="{ 'rotate-90': open }"></i>
                    </button>
                    <div x-show="open" x-collapse class="ml-6 mt-1 space-y-1">
                        <a href="{{ route('marketing.seo.index') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.seo.*') ? 'bg-purple-50 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-search mr-2 h-4 w-4"></i>
                            SEO Management
                        </a>
                        <a href="{{ route('marketing.analytics.index') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.analytics.*') ? 'bg-purple-50 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-chart-line mr-2 h-4 w-4"></i>
                            Analytics
                        </a>
                        <a href="{{ route('marketing.reports.index') }}"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('marketing.reports.*') ? 'bg-purple-50 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }}">
                            <i class="fas fa-file-chart-line mr-2 h-4 w-4"></i>
                            Marketing Reports
                        </a>
                    </div>
                </div>

                <!-- Activity Logs -->
                <a href="{{ route('activity-logs.index') }}"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('activity-logs.*') ? 'bg-red-100 text-red-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i class="fas fa-history mr-3 flex-shrink-0 h-5 w-5 {{ request()->routeIs('activity-logs.*') ? 'text-red-500' : 'text-gray-400 group-hover:text-gray-500' }}"></i>
                    Activity Logs
                </a>
                @endif
            </nav>
        </div>
    </div>
</div>

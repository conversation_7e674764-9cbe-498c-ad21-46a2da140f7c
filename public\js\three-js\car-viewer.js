/**
 * 3D Car Viewer Component
 * Handles interactive 3D car display with rotation and zoom
 */
class CarViewer3D {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            autoRotate: true,
            autoRotateSpeed: 0.5,
            enableZoom: true,
            enablePan: false,
            enableDamping: true,
            dampingFactor: 0.05,
            minDistance: 2,
            maxDistance: 10,
            fov: 75,
            backgroundColor: 0x000000,
            backgroundAlpha: 0,
            ...options
        };

        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.carModel = null;
        this.lights = [];
        this.animationId = null;
        this.isLoaded = false;

        this.init();
    }

    init() {
        if (!this.container) {
            console.error('CarViewer3D: Container not found');
            return;
        }

        this.setupScene();
        this.setupCamera();
        this.setupRenderer();
        this.setupLights();
        this.setupControls();
        this.createCarPlaceholder();
        this.handleResize();
        this.animate();

        // Load Three.js dynamically if not available
        if (typeof THREE === 'undefined') {
            this.loadThreeJS();
        }
    }

    setupScene() {
        if (typeof THREE !== 'undefined') {
            this.scene = new THREE.Scene();
            this.scene.background = new THREE.Color(this.options.backgroundColor);
        } else {
            // Fallback: Create a simple placeholder
            this.createFallbackViewer();
        }
    }

    setupCamera() {
        if (typeof THREE !== 'undefined') {
            const aspect = this.container.clientWidth / this.container.clientHeight;
            this.camera = new THREE.PerspectiveCamera(this.options.fov, aspect, 0.1, 1000);
            this.camera.position.set(5, 2, 5);
            this.camera.lookAt(0, 0, 0);
        }
    }

    setupRenderer() {
        if (typeof THREE !== 'undefined') {
            this.renderer = new THREE.WebGLRenderer({ 
                antialias: true, 
                alpha: this.options.backgroundAlpha < 1 
            });
            this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
            this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            this.renderer.outputEncoding = THREE.sRGBEncoding;
            this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
            this.renderer.toneMappingExposure = 1;
            
            this.container.appendChild(this.renderer.domElement);
        }
    }

    setupLights() {
        if (typeof THREE === 'undefined') return;

        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        this.lights.push(ambientLight);

        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        this.scene.add(directionalLight);
        this.lights.push(directionalLight);

        // Fill light
        const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
        fillLight.position.set(-10, 5, -5);
        this.scene.add(fillLight);
        this.lights.push(fillLight);

        // Rim light
        const rimLight = new THREE.DirectionalLight(0xff8040, 0.2);
        rimLight.position.set(0, 5, -10);
        this.scene.add(rimLight);
        this.lights.push(rimLight);
    }

    setupControls() {
        if (typeof THREE === 'undefined' || !this.camera || !this.renderer) return;

        // Simple orbit controls implementation
        this.controls = {
            enabled: true,
            autoRotate: this.options.autoRotate,
            autoRotateSpeed: this.options.autoRotateSpeed,
            enableZoom: this.options.enableZoom,
            enablePan: this.options.enablePan,
            enableDamping: this.options.enableDamping,
            dampingFactor: this.options.dampingFactor,
            minDistance: this.options.minDistance,
            maxDistance: this.options.maxDistance,
            
            // Mouse interaction state
            isMouseDown: false,
            mouseX: 0,
            mouseY: 0,
            targetRotationX: 0,
            targetRotationY: 0,
            currentRotationX: 0,
            currentRotationY: 0,
            
            update: () => {
                if (this.controls.autoRotate && !this.controls.isMouseDown) {
                    this.controls.targetRotationY += this.controls.autoRotateSpeed * 0.01;
                }
                
                // Smooth rotation
                this.controls.currentRotationX += (this.controls.targetRotationX - this.controls.currentRotationX) * this.controls.dampingFactor;
                this.controls.currentRotationY += (this.controls.targetRotationY - this.controls.currentRotationY) * this.controls.dampingFactor;
                
                // Apply rotation to camera
                const radius = 5;
                this.camera.position.x = Math.cos(this.controls.currentRotationY) * Math.cos(this.controls.currentRotationX) * radius;
                this.camera.position.y = Math.sin(this.controls.currentRotationX) * radius + 2;
                this.camera.position.z = Math.sin(this.controls.currentRotationY) * Math.cos(this.controls.currentRotationX) * radius;
                this.camera.lookAt(0, 0, 0);
            }
        };

        this.setupMouseControls();
        this.setupTouchControls();
    }

    setupMouseControls() {
        const canvas = this.renderer?.domElement;
        if (!canvas) return;

        canvas.addEventListener('mousedown', (event) => {
            this.controls.isMouseDown = true;
            this.controls.mouseX = event.clientX;
            this.controls.mouseY = event.clientY;
            canvas.style.cursor = 'grabbing';
        });

        canvas.addEventListener('mousemove', (event) => {
            if (!this.controls.isMouseDown) return;
            
            const deltaX = event.clientX - this.controls.mouseX;
            const deltaY = event.clientY - this.controls.mouseY;
            
            this.controls.targetRotationY += deltaX * 0.01;
            this.controls.targetRotationX -= deltaY * 0.01;
            
            // Clamp vertical rotation
            this.controls.targetRotationX = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.controls.targetRotationX));
            
            this.controls.mouseX = event.clientX;
            this.controls.mouseY = event.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            this.controls.isMouseDown = false;
            canvas.style.cursor = 'grab';
        });

        canvas.addEventListener('wheel', (event) => {
            if (!this.controls.enableZoom) return;
            event.preventDefault();
            
            const scale = event.deltaY > 0 ? 1.1 : 0.9;
            const newDistance = this.camera.position.length() * scale;
            
            if (newDistance >= this.controls.minDistance && newDistance <= this.controls.maxDistance) {
                this.camera.position.multiplyScalar(scale);
            }
        });

        canvas.style.cursor = 'grab';
    }

    setupTouchControls() {
        const canvas = this.renderer?.domElement;
        if (!canvas) return;

        let lastTouchX = 0;
        let lastTouchY = 0;

        canvas.addEventListener('touchstart', (event) => {
            event.preventDefault();
            if (event.touches.length === 1) {
                this.controls.isMouseDown = true;
                lastTouchX = event.touches[0].clientX;
                lastTouchY = event.touches[0].clientY;
            }
        });

        canvas.addEventListener('touchmove', (event) => {
            event.preventDefault();
            if (!this.controls.isMouseDown || event.touches.length !== 1) return;
            
            const deltaX = event.touches[0].clientX - lastTouchX;
            const deltaY = event.touches[0].clientY - lastTouchY;
            
            this.controls.targetRotationY += deltaX * 0.01;
            this.controls.targetRotationX -= deltaY * 0.01;
            
            // Clamp vertical rotation
            this.controls.targetRotationX = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.controls.targetRotationX));
            
            lastTouchX = event.touches[0].clientX;
            lastTouchY = event.touches[0].clientY;
        });

        canvas.addEventListener('touchend', () => {
            this.controls.isMouseDown = false;
        });
    }

    createCarPlaceholder() {
        if (typeof THREE === 'undefined') return;

        // Create a simple car-like shape as placeholder
        const carGroup = new THREE.Group();

        // Car body
        const bodyGeometry = new THREE.BoxGeometry(4, 1, 2);
        const bodyMaterial = new THREE.MeshPhongMaterial({ color: 0x3366cc });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.5;
        body.castShadow = true;
        body.receiveShadow = true;
        carGroup.add(body);

        // Car roof
        const roofGeometry = new THREE.BoxGeometry(2.5, 0.8, 1.8);
        const roofMaterial = new THREE.MeshPhongMaterial({ color: 0x2255aa });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.set(0, 1.4, 0);
        roof.castShadow = true;
        carGroup.add(roof);

        // Wheels
        const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 16);
        const wheelMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
        
        const wheelPositions = [
            [-1.3, 0, 1.2],
            [1.3, 0, 1.2],
            [-1.3, 0, -1.2],
            [1.3, 0, -1.2]
        ];

        wheelPositions.forEach(pos => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.position.set(pos[0], pos[1], pos[2]);
            wheel.rotation.z = Math.PI / 2;
            wheel.castShadow = true;
            carGroup.add(wheel);
        });

        // Ground plane
        const groundGeometry = new THREE.PlaneGeometry(20, 20);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff, transparent: true, opacity: 0.1 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -0.5;
        ground.receiveShadow = true;
        this.scene.add(ground);

        this.scene.add(carGroup);
        this.carModel = carGroup;
        this.isLoaded = true;
    }

    createFallbackViewer() {
        // Create a CSS-based fallback when Three.js is not available
        this.container.innerHTML = `
            <div class="fallback-car-viewer">
                <div class="car-placeholder">
                    <i class="fas fa-car"></i>
                    <p>3D Car Viewer</p>
                    <small>Interactive 3D view coming soon</small>
                </div>
            </div>
        `;

        // Add CSS for fallback
        const style = document.createElement('style');
        style.textContent = `
            .fallback-car-viewer {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 1rem;
                color: white;
                text-align: center;
            }
            .car-placeholder i {
                font-size: 4rem;
                margin-bottom: 1rem;
                opacity: 0.8;
            }
            .car-placeholder p {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 0.5rem;
            }
            .car-placeholder small {
                opacity: 0.7;
            }
        `;
        document.head.appendChild(style);
    }

    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());

        if (this.controls && this.controls.update) {
            this.controls.update();
        }

        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    handleResize() {
        window.addEventListener('resize', () => {
            if (!this.camera || !this.renderer) return;

            const width = this.container.clientWidth;
            const height = this.container.clientHeight;

            this.camera.aspect = width / height;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(width, height);
        });
    }

    loadThreeJS() {
        // Dynamically load Three.js if not available
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
        script.onload = () => {
            this.init(); // Reinitialize with Three.js
        };
        document.head.appendChild(script);
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        if (this.renderer) {
            this.renderer.dispose();
            if (this.renderer.domElement.parentNode) {
                this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
            }
        }

        // Clean up scene
        if (this.scene) {
            this.scene.clear();
        }
    }

    // Public methods
    setAutoRotate(enabled) {
        if (this.controls) {
            this.controls.autoRotate = enabled;
        }
    }

    resetCamera() {
        if (this.controls) {
            this.controls.targetRotationX = 0;
            this.controls.targetRotationY = 0;
        }
    }

    loadCarModel(modelUrl) {
        // TODO: Implement GLTF/GLB model loading
        console.log('Loading car model:', modelUrl);
    }
}

// Export for use in other scripts
window.CarViewer3D = CarViewer3D;

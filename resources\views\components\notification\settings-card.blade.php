@props(['preferences'])

<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6 bg-white border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
            <i class="fas fa-bell mr-2 text-indigo-600"></i>
            {{ __('Notification Settings') }}
        </h3>
        
        <div class="space-y-6">
            <!-- Notification Channels -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-3">{{ __('Notification Channels') }}</h4>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="notification_app" 
                            name="notification_app" 
                            value="1"
                            {{ $preferences->notification_app ? 'checked' : '' }}
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="notification_app" class="ml-2 block text-sm text-gray-700">
                            <i class="fas fa-mobile-alt mr-1 text-blue-500"></i>
                            {{ __('In-App Notifications') }}
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="notification_email" 
                            name="notification_email" 
                            value="1"
                            {{ $preferences->notification_email ? 'checked' : '' }}
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="notification_email" class="ml-2 block text-sm text-gray-700">
                            <i class="fas fa-envelope mr-1 text-green-500"></i>
                            {{ __('Email Notifications') }}
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="notification_sms" 
                            name="notification_sms" 
                            value="1"
                            {{ $preferences->notification_sms ? 'checked' : '' }}
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="notification_sms" class="ml-2 block text-sm text-gray-700">
                            <i class="fas fa-sms mr-1 text-yellow-500"></i>
                            {{ __('SMS Notifications') }} <span class="text-xs text-gray-500">({{ __('Coming Soon') }})</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Notification Types -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-3">{{ __('Notification Types') }}</h4>
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <input 
                                    type="checkbox" 
                                    id="notification_repair_phase" 
                                    name="notification_repair_phase" 
                                    value="1"
                                    {{ $preferences->notification_repair_phase ? 'checked' : '' }}
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                >
                                <label for="notification_repair_phase" class="ml-2 block text-sm font-medium text-gray-700">
                                    <i class="fas fa-wrench mr-1 text-orange-500"></i>
                                    {{ __('Repair Phase Alerts') }}
                                </label>
                            </div>
                        </div>
                        <div class="ml-6">
                            <label for="repair_phase_days_threshold" class="block text-xs text-gray-600 mb-1">
                                {{ __('Alert after days in repair phase:') }}
                            </label>
                            <input 
                                type="number" 
                                id="repair_phase_days_threshold" 
                                name="repair_phase_days_threshold" 
                                value="{{ $preferences->repair_phase_days_threshold ?? 30 }}"
                                min="1" 
                                max="365"
                                class="w-20 text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                            >
                            <span class="text-xs text-gray-500 ml-1">{{ __('days') }}</span>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <input 
                                    type="checkbox" 
                                    id="notification_dealership_phase" 
                                    name="notification_dealership_phase" 
                                    value="1"
                                    {{ $preferences->notification_dealership_phase ? 'checked' : '' }}
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                >
                                <label for="notification_dealership_phase" class="ml-2 block text-sm font-medium text-gray-700">
                                    <i class="fas fa-store mr-1 text-blue-500"></i>
                                    {{ __('Dealership Phase Alerts') }}
                                </label>
                            </div>
                        </div>
                        <div class="ml-6">
                            <label for="dealership_phase_days_threshold" class="block text-xs text-gray-600 mb-1">
                                {{ __('Alert after days at dealership:') }}
                            </label>
                            <input 
                                type="number" 
                                id="dealership_phase_days_threshold" 
                                name="dealership_phase_days_threshold" 
                                value="{{ $preferences->dealership_phase_days_threshold ?? 60 }}"
                                min="1" 
                                max="365"
                                class="w-20 text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                            >
                            <span class="text-xs text-gray-500 ml-1">{{ __('days') }}</span>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <input 
                                    type="checkbox" 
                                    id="notification_budget_exceeded" 
                                    name="notification_budget_exceeded" 
                                    value="1"
                                    {{ $preferences->notification_budget_exceeded ? 'checked' : '' }}
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                >
                                <label for="notification_budget_exceeded" class="ml-2 block text-sm font-medium text-gray-700">
                                    <i class="fas fa-exclamation-triangle mr-1 text-red-500"></i>
                                    {{ __('Budget Exceeded Alerts') }}
                                </label>
                            </div>
                        </div>
                        <div class="ml-6">
                            <label for="budget_exceeded_percentage" class="block text-xs text-gray-600 mb-1">
                                {{ __('Alert when budget exceeded by:') }}
                            </label>
                            <input 
                                type="number" 
                                id="budget_exceeded_percentage" 
                                name="budget_exceeded_percentage" 
                                value="{{ $preferences->budget_exceeded_percentage ?? 20 }}"
                                min="1" 
                                max="100"
                                class="w-20 text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                            >
                            <span class="text-xs text-gray-500 ml-1">{{ __('%') }}</span>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <input 
                                type="checkbox" 
                                id="notification_opportunity" 
                                name="notification_opportunity" 
                                value="1"
                                {{ $preferences->notification_opportunity ? 'checked' : '' }}
                                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                            >
                            <label for="notification_opportunity" class="ml-2 block text-sm font-medium text-gray-700">
                                <i class="fas fa-chart-line mr-1 text-green-500"></i>
                                {{ __('Investment Opportunity Alerts') }}
                            </label>
                        </div>
                        <p class="ml-6 text-xs text-gray-500 mt-1">
                            {{ __('Get notified about high-scoring investment opportunities from our AI analysis.') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="border-t border-gray-200 pt-4">
                <h4 class="text-md font-medium text-gray-700 mb-3">{{ __('Quick Actions') }}</h4>
                <div class="flex flex-wrap gap-2">
                    <button 
                        type="button" 
                        onclick="enableAllNotifications()"
                        class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <i class="fas fa-check-circle mr-1 text-green-500"></i>
                        {{ __('Enable All') }}
                    </button>
                    
                    <button 
                        type="button" 
                        onclick="disableAllNotifications()"
                        class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <i class="fas fa-times-circle mr-1 text-red-500"></i>
                        {{ __('Disable All') }}
                    </button>
                    
                    <a 
                        href="{{ route('notifications.test') }}"
                        class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <i class="fas fa-bell mr-1 text-blue-500"></i>
                        {{ __('Send Test Notification') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function enableAllNotifications() {
    document.querySelectorAll('input[type="checkbox"][name^="notification_"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function disableAllNotifications() {
    document.querySelectorAll('input[type="checkbox"][name^="notification_"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}
</script>
@endpush

@extends('public.layouts.app')

@section('title', 'AutoLux - Premium Luxury Cars | Exclusive Collection of High-End Vehicles')
@section('meta_description', 'Discover the finest luxury cars at AutoLux. Exclusive collection of premium vehicles from prestigious brands including BMW, Mercedes-Benz, Audi, Porsche, and more. Experience automotive excellence.')

@section('content')
<!-- Hero Section with 3D Showcase -->
<section class="relative bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white overflow-hidden min-h-[80vh] flex items-center">
    <!-- Animated Background Particles -->
    <div class="absolute inset-0" id="particles-background"></div>
    <div class="absolute inset-0 bg-black opacity-30"></div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32 w-full">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Left Content with Kinetic Typography -->
            <div class="text-center lg:text-left" data-scroll-animation="slide-right">
                <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                    <span data-kinetic-text data-animation-type="reveal" data-split-by="words" data-stagger="100">
                        Experience
                    </span>
                    <span class="text-transparent bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600 bg-clip-text" data-kinetic-text data-animation-type="morphing" data-split-by="chars" data-delay="500" data-stagger="50">
                        Automotive Luxury
                    </span>
                    <span data-kinetic-text data-animation-type="reveal" data-split-by="words" data-delay="1000" data-stagger="100">
                        Redefined
                    </span>
                </h1>
                <p class="text-xl lg:text-2xl text-gray-300 mb-8 leading-relaxed"
                   data-kinetic-text data-animation-type="typewriter" data-split-by="chars" data-delay="1500" data-stagger="30">
                    Curated collection of the world's finest luxury vehicles. From prestigious European marques to exotic supercars.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start" data-scroll-animation="fade-in" data-animation-delay="2000">
                    <a href="{{ route('public.cars.index') }}"
                       class="bg-gradient-to-r from-amber-500 to-yellow-600 text-black px-8 py-4 rounded-lg text-lg font-semibold hover:from-amber-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                       data-click-effect>
                        <i class="fas fa-crown mr-2"></i>
                        Explore Collection
                    </a>
                    <button data-search-trigger
                            class="bg-transparent border-2 border-amber-400 text-amber-400 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-amber-400 hover:text-black transition-all duration-300 transform hover:scale-105"
                            data-click-effect>
                        <i class="fas fa-search mr-2"></i>
                        Find Your Dream Car
                    </button>
                </div>
            </div>

            <!-- Right 3D Car Showcase -->
            <div class="relative" data-scroll-animation="slide-left">
                <div class="relative">
                    <!-- 3D Car Viewer Container -->
                    <div id="hero-car-viewer" class="w-full h-96 lg:h-[500px] rounded-2xl shadow-2xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600">
                        <!-- Fallback content will be replaced by 3D viewer -->
                        <div class="flex items-center justify-center h-full text-center p-8">
                            <div>
                                <i class="fas fa-car text-6xl mb-4 opacity-80 animate-pulse"></i>
                                <h3 class="text-2xl font-bold mb-2">3D Car Showcase</h3>
                                <p class="text-blue-100">Loading interactive 3D viewer...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Floating Info Cards -->
                    <div class="absolute -top-4 -left-4 bg-gradient-to-r from-white to-gray-50 text-gray-900 rounded-xl p-4 shadow-xl border border-gray-100"
                         data-scroll-animation="scale-in" data-animation-delay="500">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-certificate text-amber-500"></i>
                            <span class="font-semibold">Certified Luxury</span>
                        </div>
                    </div>

                    <div class="absolute -bottom-4 -right-4 bg-gradient-to-r from-white to-gray-50 text-gray-900 rounded-xl p-4 shadow-xl border border-gray-100"
                         data-scroll-animation="scale-in" data-animation-delay="700">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-concierge-bell text-blue-600"></i>
                            <span class="font-semibold">White Glove Service</span>
                        </div>
                    </div>

                    <div class="absolute top-1/2 -left-8 bg-gradient-to-r from-white to-gray-50 text-gray-900 rounded-xl p-4 shadow-xl border border-gray-100 transform -translate-y-1/2"
                         data-scroll-animation="scale-in" data-animation-delay="900">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-amber-600">{{ number_format($stats['total_cars'] ?? 150) }}</div>
                            <div class="text-sm">Exclusive Vehicles</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center">
            <div class="animate-bounce">
                <i class="fas fa-chevron-down text-2xl opacity-60"></i>
            </div>
            <p class="text-sm opacity-60 mt-2">Scroll to explore</p>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="bg-gradient-to-r from-gray-50 to-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Trusted by Luxury Car Enthusiasts</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Experience the difference with AutoLux's commitment to excellence and unparalleled service.</p>
        </div>
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center group">
                <div class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-amber-500 to-yellow-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">{{ number_format($stats['total_cars'] ?? 150) }}+</div>
                <div class="text-gray-600 font-medium">Luxury Vehicles</div>
            </div>
            <div class="text-center group">
                <div class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">{{ number_format($stats['cars_sold'] ?? 500) }}+</div>
                <div class="text-gray-600 font-medium">Satisfied Clients</div>
            </div>
            <div class="text-center group">
                <div class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">{{ number_format($stats['verified_dealers'] ?? 15) }}</div>
                <div class="text-gray-600 font-medium">Premium Partners</div>
            </div>
            <div class="text-center group">
                <div class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">{{ number_format($stats['years_experience'] ?? 15) }}+</div>
                <div class="text-gray-600 font-medium">Years Excellence</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Cars Section with Bento Grid -->
@if($featuredCars->count() > 0)
<section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12" data-scroll-animation="fade-in">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4"
                data-kinetic-text data-animation-type="reveal" data-split-by="words" data-stagger="100">
                Exclusive Collection
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto"
               data-kinetic-text data-animation-type="typewriter" data-split-by="chars" data-delay="500" data-stagger="20">
                Meticulously curated luxury vehicles from the world's most prestigious automotive brands.
            </p>
        </div>

        <!-- Bento Grid Layout -->
        <div class="bento-grid">
            @foreach($featuredCars as $index => $car)
            <div class="bento-item bento-item--featured-car bento-item--animate-in {{ $index === 0 ? 'bento-item--large' : ($index === 1 ? 'bento-item--medium' : 'bento-item--small') }}"
                 style="animation-delay: {{ $index * 100 }}ms;">
                <div class="bento-item__header">
                    <div class="flex justify-between items-start mb-4">
                        <span class="bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            Featured
                        </span>
                        @if($car->year >= 2022)
                        <span class="bg-gradient-to-r from-amber-500 to-yellow-600 text-black px-3 py-1 rounded-full text-sm font-semibold">
                            Latest Model
                        </span>
                        @elseif(in_array(strtolower($car->make), ['mercedes-benz', 'bmw', 'audi', 'porsche', 'lexus']))
                        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            Premium Brand
                        </span>
                        @endif
                    </div>

                    <img src="{{ $car->primary_image_url }}"
                         alt="{{ $car->display_name }}"
                         class="bento-item__image">
                </div>

                <div class="bento-item__content">
                    <h3 class="bento-item__title">{{ $car->display_name }}</h3>

                    <div class="flex justify-between items-center mb-4">
                        <span class="text-2xl font-bold">R{{ number_format($car->purchase_price) }}</span>
                        @if($car->mileage < 30000)
                        <span class="bg-white bg-opacity-20 px-2 py-1 rounded text-sm font-semibold">
                            Exceptional Condition
                        </span>
                        @elseif($car->mileage < 60000)
                        <span class="bg-white bg-opacity-20 px-2 py-1 rounded text-sm font-semibold">
                            Premium Condition
                        </span>
                        @endif
                    </div>

                    <div class="grid grid-cols-2 gap-2 text-sm mb-4 opacity-90">
                        <div><i class="fas fa-calendar mr-1"></i> {{ $car->year }}</div>
                        <div><i class="fas fa-tachometer-alt mr-1"></i> {{ number_format($car->mileage) }}k</div>
                        <div><i class="fas fa-gas-pump mr-1"></i> {{ ucfirst($car->fuel_type) }}</div>
                        <div><i class="fas fa-cogs mr-1"></i> {{ ucfirst($car->transmission) }}</div>
                    </div>
                </div>

                <div class="bento-item__footer">
                    <a href="{{ route('public.cars.show', $car) }}"
                       class="bento-item__button w-full"
                       data-click-effect>
                        View Details
                    </a>
                </div>
            </div>
            @endforeach

            <!-- Additional Bento Items for Visual Interest -->
            <div class="bento-item bento-item--stats bento-item--animate-in" style="animation-delay: {{ $featuredCars->count() * 100 }}ms;">
                <div class="bento-item__content text-center">
                    <div class="bento-item__icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="bento-item__number">{{ number_format($stats['total_cars']) }}</div>
                    <div class="bento-item__title">Cars Available</div>
                    <p class="opacity-80">Premium vehicles ready for investment</p>
                </div>
            </div>

            <div class="bento-item bento-item--cta bento-item--animate-in" style="animation-delay: {{ ($featuredCars->count() + 1) * 100 }}ms;">
                <div class="bento-item__content text-center">
                    <div class="bento-item__icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="bento-item__title">Find Your Perfect Car</div>
                    <p class="mb-4">Use our advanced search to discover your ideal investment vehicle</p>
                    <button data-search-trigger class="bento-item__button" data-click-effect>
                        Start Searching
                    </button>
                </div>
            </div>

            <div class="bento-item bento-item--investment bento-item--animate-in" style="animation-delay: {{ ($featuredCars->count() + 2) * 100 }}ms;">
                <div class="bento-item__content">
                    <div class="bento-item__icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="bento-item__title">Investment Success</div>
                    <div class="text-3xl font-bold text-green-600 mb-2">{{ number_format($stats['average_roi'] ?? 0, 1) }}%</div>
                    <p>Average return on investment for our clients</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-12" data-scroll-animation="fade-in">
            <a href="{{ route('public.cars.index') }}"
               class="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors"
               data-click-effect>
                View All Cars
            </a>
        </div>
    </div>
</section>
@endif

<!-- Why Choose Us Section -->
<section class="bg-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                <span class="bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">Why Choose</span>
                <span class="text-gray-900"> AutoLux?</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the pinnacle of luxury automotive excellence with our unparalleled service and exclusive collection.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-2xl p-8 border border-amber-200 text-center group hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="w-20 h-20 bg-gradient-to-r from-amber-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-certificate text-2xl text-white"></i>
                </div>
                <h4 class="font-bold text-gray-900 mb-3 text-lg">Certified Luxury</h4>
                <p class="text-gray-600 text-sm leading-relaxed">Every vehicle undergoes rigorous certification ensuring the highest standards of luxury and performance.</p>
            </div>

            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-200 text-center group hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-concierge-bell text-2xl text-white"></i>
                </div>
                <h4 class="font-bold text-gray-900 mb-3 text-lg">Concierge Service</h4>
                <p class="text-gray-600 text-sm leading-relaxed">Personalized white-glove service from initial inquiry to final delivery and beyond.</p>
            </div>

            <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl p-8 border border-purple-200 text-center group hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-crown text-2xl text-white"></i>
                </div>
                <h4 class="font-bold text-gray-900 mb-3 text-lg">Exclusive Access</h4>
                <p class="text-gray-600 text-sm leading-relaxed">Access to rare and exclusive vehicles not available through traditional channels.</p>
            </div>

            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-200 text-center group hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="w-20 h-20 bg-gradient-to-r from-green-600 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shield-alt text-2xl text-white"></i>
                </div>
                <h4 class="font-bold text-gray-900 mb-3 text-lg">Lifetime Assurance</h4>
                <p class="text-gray-600 text-sm leading-relaxed">Comprehensive protection and ongoing support for your luxury automotive investment.</p>
            </div>
        </div>
    </div>
</section>

<!-- Popular Makes Section -->
<section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Popular Makes</h2>
            <p class="text-xl text-gray-600">Browse by your favorite car brands</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            @foreach($popularMakes as $make)
            <a href="{{ route('public.cars.index', ['make' => $make->make]) }}"
               class="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
                <div class="text-2xl font-bold text-gray-900 mb-2">{{ $make->make }}</div>
                <div class="text-sm text-gray-600">{{ $make->count }} cars</div>
            </a>
            @endforeach
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-gradient-to-r from-gray-900 via-black to-gray-900 text-white py-20 relative overflow-hidden">
    <!-- Subtle 3D Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-32 h-32 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full blur-xl"></div>
        <div class="absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full blur-xl"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <h2 class="text-4xl lg:text-5xl font-bold mb-6">
            <span class="bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600 bg-clip-text text-transparent">
                Experience Luxury
            </span>
            <br>
            <span class="text-white">Like Never Before</span>
        </h2>
        <p class="text-xl mb-10 max-w-3xl mx-auto text-gray-300">
            Discover your next luxury vehicle with AutoLux's exclusive collection. From exotic supercars to prestigious sedans, we curate only the finest automobiles for discerning enthusiasts.
        </p>
        <div class="flex flex-col sm:flex-row gap-6 justify-center">
            <a href="{{ route('public.cars.index') }}"
               class="bg-gradient-to-r from-amber-500 to-yellow-600 text-black px-10 py-4 rounded-xl text-lg font-semibold hover:from-amber-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-xl">
                <i class="fas fa-crown mr-2"></i>
                Explore Luxury Collection
            </a>
            <a href="{{ route('public.contact.index') }}"
               class="bg-transparent border-2 border-amber-400 text-amber-400 px-10 py-4 rounded-xl text-lg font-semibold hover:bg-amber-400 hover:text-black transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-phone mr-2"></i>
                Contact Us
            </a>
        </div>
    </div>
</section>
@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('css/components/bento-grid.css') }}">
<style>
    /* Particle background styles */
    #particles-background {
        background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 80%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
        animation: particleFloat 20s ease-in-out infinite;
    }

    @keyframes particleFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(120deg); }
        66% { transform: translateY(10px) rotate(240deg); }
    }

    /* Enhanced animations */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .animate-float {
        animation: float 3s ease-in-out infinite;
    }

    /* Gradient text effect */
    .gradient-text {
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 300% 300%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradientShift 4s ease infinite;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/three-js/car-viewer.js') }}"></script>
<script src="{{ asset('js/animations/kinetic-typography.js') }}"></script>
<script src="{{ asset('js/animations/micro-interactions.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize 3D Car Viewer
    const heroCarViewer = new CarViewer3D('hero-car-viewer', {
        autoRotate: true,
        autoRotateSpeed: 0.3,
        enableZoom: true,
        enablePan: false
    });

    // Create animated particle background
    createParticleBackground();

    // Initialize scroll animations with stagger
    initializeScrollAnimations();

    // Add floating animation to floating cards
    const floatingCards = document.querySelectorAll('.absolute');
    floatingCards.forEach((card, index) => {
        card.style.animation = `float ${3 + index * 0.5}s ease-in-out infinite`;
        card.style.animationDelay = `${index * 0.2}s`;
    });

    // Initialize counter animations
    initializeCounterAnimations();

    // Add subtle parallax effect to hero section (reduced intensity)
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('section');
        if (heroSection && scrolled < window.innerHeight) {
            heroSection.style.transform = `translateY(${scrolled * 0.2}px)`;
        }
    });
});

function createParticleBackground() {
    const particlesContainer = document.getElementById('particles-background');
    if (!particlesContainer) return;

    // Create floating particles
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: rgba(255, 255, 255, ${Math.random() * 0.5 + 0.1});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: particleMove ${Math.random() * 20 + 10}s linear infinite;
            animation-delay: ${Math.random() * 5}s;
        `;
        particlesContainer.appendChild(particle);
    }

    // Add particle movement keyframes
    const style = document.createElement('style');
    style.textContent = `
        @keyframes particleMove {
            0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) translateX(50px) rotate(360deg); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-scroll-animation]');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach(element => {
        observer.observe(element);
    });

    // Add CSS for scroll animations
    const style = document.createElement('style');
    style.textContent = `
        [data-scroll-animation] {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        [data-scroll-animation].animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        [data-scroll-animation="slide-left"] {
            transform: translateX(30px);
        }

        [data-scroll-animation="slide-left"].animate-in {
            transform: translateX(0);
        }

        [data-scroll-animation="slide-right"] {
            transform: translateX(-30px);
        }

        [data-scroll-animation="slide-right"].animate-in {
            transform: translateX(0);
        }

        [data-scroll-animation="scale-in"] {
            transform: scale(0.8);
        }

        [data-scroll-animation="scale-in"].animate-in {
            transform: scale(1);
        }
    `;
    document.head.appendChild(style);
}

function initializeCounterAnimations() {
    const counters = document.querySelectorAll('.bento-item__number');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
            }
        });
    });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.textContent.replace(/,/g, ''));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;

    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// Add enhanced hover effects for bento items
document.addEventListener('DOMContentLoaded', function() {
    const bentoItems = document.querySelectorAll('.bento-item');

    bentoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });
});
</script>
@endpush

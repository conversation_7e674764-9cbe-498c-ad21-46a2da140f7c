name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          repository: delan75/I-fixit
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, bcmath, pdo, pdo_mysql, exif, pcntl, gd

      - name: Install Composer Dependencies
        run: composer install --no-dev --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install NPM Dependencies
        run: npm install

      - name: Build Assets
        run: npm run build

      - name: Create i-fixit Directory Structure
        run: |
          mkdir -p deploy/i-fixit
          mkdir -p deploy/build

      - name: Copy Laravel Application to i-fixit Directory
        run: |
          # Copy all Laravel application files except public directory to i-fixit
          rsync -av --exclude='public' --exclude='node_modules' --exclude='.git' --exclude='.github' --exclude='deploy' . deploy/i-fixit/

      - name: Copy Public Files
        run: |
          # Copy public directory contents to deploy root
          rsync -av public/ deploy/

      - name: Copy Build Assets
        run: |
          # Copy build assets to deploy/build
          if [ -d "public/build" ]; then
            rsync -av public/build/ deploy/build/
          fi

      # Skipping index.php update - will be done manually
      - name: Note about index.php
        run: |
          echo "NOTE: index.php needs to be manually updated to point to the i-fixit directory"
          echo "Please update paths in public_html/index.php to use __DIR__.'/i-fixit/' instead of __DIR__.'/../'"

      - name: Ensure Storage Directory Permissions
        run: |
          chmod -R 775 deploy/i-fixit/storage

      - name: Deploy to Hosting
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: ************
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          local-dir: ./deploy/
          server-dir: /public_html/
          protocol: ftp
          port: 21
          timeout: 120000
          exclude: |
            **/.git*
            **/.git*/**
            **/node_modules/**
            **/.env.example
            **/vendor/symfony/var-dumper/**
            **/vendor/symfony/translation/**
            **/vendor/symfony/string/**
            **/vendor/symfony/service-contracts/**
            **/vendor/symfony/routing/**
            **/vendor/symfony/process/**
            **/vendor/symfony/polyfill-**/**
            **/vendor/symfony/mime/**
            **/vendor/symfony/http-foundation/**
            **/vendor/symfony/finder/**
            **/vendor/symfony/event-dispatcher/**
            **/vendor/symfony/error-handler/**
            **/vendor/symfony/deprecation-contracts/**
            **/vendor/symfony/css-selector/**
            **/vendor/symfony/console/**
            **/tests/**
        env:
          FTP_DEBUG: 1

      - name: Run Post-Deployment Commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          password: ${{ secrets.SSH_PASSWORD }}
          port: ${{ secrets.SSH_PORT }}
          script: |
            cd ~/public_html/i-fixit
            php artisan migrate --force
            php artisan config:clear
            php artisan cache:clear
            php artisan view:clear
            php artisan route:clear
            php artisan optimize

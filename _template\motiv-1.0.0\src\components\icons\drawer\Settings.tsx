import { SvgIcon, SvgIconProps } from '@mui/material';

const Settings = (props: SvgIconProps) => {
  return (
    <SvgIcon width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.6244 5.909C16.6331 5.25809 16.3891 4.60444 15.8924 4.10777C15.3957 3.6111 14.7421 3.3671 14.0912 3.37576C14.0909 3.37576 14.0914 3.37576 14.0912 3.37576C13.7314 3.38065 13.3717 3.46274 13.0407 3.622C12.922 3.67908 12.8069 3.74607 12.6967 3.82297C12.6542 3.85259 12.5995 3.85934 12.5517 3.83951C12.5039 3.8197 12.47 3.77627 12.4609 3.72534C12.4374 3.59299 12.4034 3.46424 12.3598 3.33998C12.2383 2.99325 12.0426 2.68143 11.7916 2.42358C11.7915 2.4234 11.7918 2.42375 11.7916 2.42358C11.3375 1.95719 10.7022 1.66699 9.99984 1.66699C9.29744 1.66699 8.66271 1.95666 8.20857 2.42305C8.2084 2.42322 8.20874 2.42287 8.20857 2.42305C7.95763 2.6809 7.76137 2.99325 7.63987 3.33998C7.59632 3.46424 7.56232 3.59299 7.53873 3.72534C7.52965 3.77626 7.49574 3.8197 7.44796 3.83951C7.40014 3.85934 7.34548 3.85259 7.30302 3.82297C7.19275 3.74607 7.07767 3.67908 6.95902 3.622C6.62793 3.46274 6.26902 3.38066 5.90925 3.37577C5.909 3.37577 5.90949 3.37577 5.90925 3.37577C5.25833 3.36711 4.60395 3.6111 4.10728 4.10777C3.61061 4.60444 3.36661 5.25809 3.37527 5.909C3.37527 5.90876 3.37528 5.90925 3.37527 5.909C3.38016 6.26877 3.46225 6.62841 3.62151 6.9595C3.67859 7.07816 3.74558 7.19324 3.82248 7.3035C3.8521 7.34597 3.85886 7.40062 3.83902 7.44845C3.81921 7.49623 3.77578 7.53014 3.72485 7.53922C3.59249 7.56281 3.46375 7.59682 3.33948 7.64036C2.99276 7.76186 2.68094 7.95761 2.42309 8.20854C2.42291 8.20871 2.42326 8.20837 2.42309 8.20854C1.9567 8.66268 1.6665 9.29793 1.6665 10.0003C1.6665 10.7027 1.95617 11.3375 2.42256 11.7916C2.42239 11.7914 2.42274 11.7918 2.42256 11.7916C2.68041 12.0425 2.99276 12.2388 3.33948 12.3603C3.46374 12.4038 3.59249 12.4378 3.72485 12.4614C3.77578 12.4705 3.81921 12.5044 3.83902 12.5522C3.85886 12.6 3.8521 12.6547 3.82248 12.6971C3.74558 12.8074 3.67859 12.9225 3.62151 13.0412C3.46225 13.3722 3.38017 13.7312 3.37528 14.0909C3.37528 14.0912 3.37529 14.0907 3.37528 14.0909C3.36662 14.7418 3.61061 15.3962 4.10728 15.8929C4.60395 16.3896 5.2576 16.6336 5.90852 16.6249C5.90827 16.6249 5.90876 16.6249 5.90852 16.6249C6.26828 16.62 6.62792 16.5379 6.959 16.3787C7.07766 16.3216 7.19274 16.2546 7.30301 16.1777C7.34548 16.1481 7.40013 16.1413 7.44796 16.1611C7.49574 16.181 7.52965 16.2244 7.53873 16.2753C7.56232 16.4077 7.59633 16.5364 7.63988 16.6607C7.76137 17.0074 7.95712 17.3192 8.20805 17.5771C8.20788 17.5769 8.20822 17.5773 8.20805 17.5771C8.66219 18.0435 9.29744 18.3337 9.99984 18.3337C10.7022 18.3337 11.337 18.044 11.7911 17.5776C11.7909 17.5778 11.7913 17.5774 11.7911 17.5776C12.042 17.3198 12.2383 17.0074 12.3598 16.6607C12.4033 16.5364 12.4374 16.4077 12.4609 16.2753C12.47 16.2244 12.5039 16.181 12.5517 16.1611C12.5995 16.1413 12.6542 16.1481 12.6967 16.1777C12.8069 16.2546 12.922 16.3216 13.0407 16.3787C13.3718 16.5379 13.7307 16.62 14.0904 16.6249C14.0902 16.6249 14.0907 16.6249 14.0904 16.6249C14.7413 16.6335 15.3957 16.3896 15.8924 15.8929C16.3891 15.3962 16.6331 14.7426 16.6244 14.0916C16.6244 14.0914 16.6244 14.0919 16.6244 14.0916C16.6195 13.7319 16.5374 13.3722 16.3782 13.0412C16.3211 12.9225 16.2541 12.8074 16.1772 12.6971C16.1476 12.6547 16.1408 12.6 16.1607 12.5522C16.1805 12.5044 16.2239 12.4705 16.2748 12.4614C16.4072 12.4378 16.5359 12.4038 16.6602 12.3603C17.0069 12.2388 17.3187 12.043 17.5766 11.7921C17.5764 11.7923 17.5768 11.7919 17.5766 11.7921C18.043 11.338 18.3332 10.7027 18.3332 10.0003C18.3332 9.29793 18.0435 8.6632 17.5771 8.20905C17.5769 8.20888 17.5773 8.20922 17.5771 8.20905C17.3193 7.95812 17.0069 7.76186 16.6602 7.64036C16.5359 7.59682 16.4072 7.56281 16.2748 7.53922C16.2239 7.53014 16.1805 7.49623 16.1607 7.44845C16.1408 7.40062 16.1476 7.34597 16.1772 7.3035C16.2541 7.19323 16.3211 7.07815 16.3782 6.95949C16.5374 6.62841 16.6195 6.26876 16.6244 5.909C16.6244 5.90876 16.6244 5.90925 16.6244 5.909ZM14.7139 5.28628C14.4252 4.9976 13.9741 4.96402 13.6501 5.19001C13.1585 5.53286 12.5056 5.62468 11.9133 5.37906C11.3225 5.13408 10.9254 4.60853 10.8201 4.01781C10.7508 3.6289 10.4081 3.33366 9.99984 3.33366C9.59158 3.33366 9.24885 3.6289 9.17953 4.01781C9.07423 4.60852 8.67715 5.13408 8.08637 5.37906C7.49407 5.62467 6.8412 5.53286 6.34961 5.19C6.02559 4.96402 5.57447 4.9976 5.28579 5.28628C4.99711 5.57496 4.96354 6.02607 5.18952 6.35009C5.53237 6.8417 5.62418 7.49456 5.37857 8.08686C5.13359 8.67763 4.60804 9.07472 4.01732 9.18002C3.62841 9.24934 3.33317 9.59207 3.33317 10.0003C3.33317 10.4086 3.62841 10.7513 4.01732 10.8206C4.60804 10.9259 5.13359 11.323 5.37857 11.9138C5.62419 12.5061 5.53237 13.159 5.18952 13.6506C4.96354 13.9746 4.99711 14.4257 5.28579 14.7144C5.57448 15.0031 6.02558 15.0366 6.3496 14.8106C6.8412 14.4678 7.49406 14.376 8.08637 14.6216C8.67714 14.8666 9.07424 15.3921 9.17953 15.9828C9.24886 16.3717 9.59158 16.667 9.99984 16.667C10.4081 16.667 10.7508 16.3717 10.8201 15.9828C10.9254 15.3921 11.3225 14.8666 11.9133 14.6216C12.5056 14.376 13.1585 14.4678 13.6501 14.8106C13.9741 15.0366 14.4252 15.0031 14.7139 14.7144C15.0026 14.4257 15.0361 13.9746 14.8102 13.6506C14.4673 13.159 14.3755 12.5061 14.6211 11.9138C14.8661 11.323 15.3916 10.9259 15.9824 10.8206C16.3713 10.7513 16.6665 10.4086 16.6665 10.0003C16.6665 9.59207 16.3713 9.24934 15.9824 9.18002C15.3916 9.07472 14.8661 8.67763 14.6211 8.08686C14.3755 7.49455 14.4673 6.84169 14.8102 6.35009C15.0361 6.02607 15.0026 5.57496 14.7139 5.28628Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.99984 11.667C10.9203 11.667 11.6665 10.9208 11.6665 10.0003C11.6665 9.07985 10.9203 8.33366 9.99984 8.33366C9.07936 8.33366 8.33317 9.07985 8.33317 10.0003C8.33317 10.9208 9.07936 11.667 9.99984 11.667ZM9.99984 13.3337C11.8408 13.3337 13.3332 11.8413 13.3332 10.0003C13.3332 8.15938 11.8408 6.66699 9.99984 6.66699C8.15889 6.66699 6.6665 8.15938 6.6665 10.0003C6.6665 11.8413 8.15889 13.3337 9.99984 13.3337Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Settings;

{"name": "car-dashboard-ui-design", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "predeploy": "vite build && cp ./dist/index.html ./dist/404.html", "deploy": "gh-pages -d dist"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.15.14", "@mui/x-data-grid": "^7.3.1", "car-dashboard-ui-design": "file:", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "react-router-dom": "^6.22.3", "vite-plugin-checker": "^0.6.4"}, "devDependencies": {"@iconify/react": "^4.1.1", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-tsconfig-paths": "^4.3.2"}}
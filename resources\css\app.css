/* Import Bento Grid Styles */
@import './components/bento-grid.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Animations */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translateY(-50%) scale(0.5);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
}

@keyframes shake {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    25% { transform: translateY(-50%) translateX(-5px); }
    75% { transform: translateY(-50%) translateX(5px); }
}

@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes particleMove {
    0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100vh) translateX(50px) rotate(360deg); opacity: 0; }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Utility Classes */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

.gradient-text {
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease infinite;
}

/* Scrollbar Hide Utility */
.scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
}

/* Sidebar Styles */
.sidebar-transition {
    transition: transform 0.3s ease-in-out;
}

/* Custom scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
    width: 4px;
}

.sidebar-scroll::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 2px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}

/* Alpine.js collapse animation */
[x-cloak] {
    display: none !important;
}

/* Scroll Animation Classes */
[data-scroll-animation] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-scroll-animation].animate-in {
    opacity: 1;
    transform: translateY(0);
}

[data-scroll-animation="slide-left"] {
    transform: translateX(30px);
}

[data-scroll-animation="slide-left"].animate-in {
    transform: translateX(0);
}

[data-scroll-animation="slide-right"] {
    transform: translateX(-30px);
}

[data-scroll-animation="slide-right"].animate-in {
    transform: translateX(0);
}

[data-scroll-animation="scale-in"] {
    transform: scale(0.8);
}

[data-scroll-animation="scale-in"].animate-in {
    transform: scale(1);
}

/* Particle Background */
#particles-background {
    background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
    animation: particleFloat 20s ease-in-out infinite;
}

/* Fallback Car Viewer */
.fallback-car-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem;
    color: white;
    text-align: center;
}

.car-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.car-placeholder p {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.car-placeholder small {
    opacity: 0.7;
}

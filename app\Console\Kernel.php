<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run the scheduled reports command every minute
        $schedule->command('reports:generate-scheduled')->everyMinute();

        // Run the car notifications check every hour
        $schedule->command('app:check-car-notifications')->hourly();

        // Run smart notifications every 6 hours
        $schedule->command('notifications:smart')->everySixHours();

        // Clean up old notifications weekly
        $schedule->command('notifications:cleanup')->weekly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

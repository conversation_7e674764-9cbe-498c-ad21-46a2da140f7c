import { SvgIcon, SvgIconProps } from '@mui/material';

const Grid = (props: SvgIconProps) => {
  return (
    <SvgIcon width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.5835 12.4997C14.2067 12.4997 13.6743 12.5518 13.12 12.6277C12.8611 12.6631 12.6636 12.8606 12.6282 13.1195C12.5523 13.6738 12.5002 14.2062 12.5002 14.583C12.5002 14.9598 12.5523 15.4922 12.6282 16.0465C12.6636 16.3054 12.8611 16.5029 13.12 16.5383C13.6743 16.6143 14.2067 16.6663 14.5835 16.6663C14.9603 16.6663 15.4927 16.6143 16.047 16.5383C16.3059 16.5029 16.5034 16.3054 16.5388 16.0465C16.6147 15.4922 16.6668 14.9598 16.6668 14.583C16.6668 14.2062 16.6147 13.6738 16.5388 13.1195C16.5034 12.8606 16.3059 12.6631 16.047 12.6277C15.4927 12.5518 14.9603 12.4997 14.5835 12.4997ZM12.8939 10.9764C11.8939 11.1134 11.1138 11.8935 10.9769 12.8934C10.8984 13.4669 10.8335 14.0898 10.8335 14.583C10.8335 15.0762 10.8984 15.6991 10.9769 16.2726C11.1138 17.2726 11.8939 18.0527 12.8939 18.1896C13.4674 18.2681 14.0903 18.333 14.5835 18.333C15.0767 18.333 15.6996 18.2681 16.2731 18.1896C17.2731 18.0527 18.0531 17.2726 18.1901 16.2726C18.2686 15.6991 18.3335 15.0762 18.3335 14.583C18.3335 14.0898 18.2686 13.4669 18.1901 12.8934C18.0531 11.8935 17.2731 11.1134 16.2731 10.9764C15.6996 10.8979 15.0767 10.833 14.5835 10.833C14.0903 10.833 13.4674 10.8979 12.8939 10.9764Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.4165 12.4997C5.03969 12.4997 4.50728 12.5518 3.95301 12.6277C3.69407 12.6631 3.49663 12.8606 3.46117 13.1195C3.38526 13.6738 3.33317 14.2062 3.33317 14.583C3.33317 14.9598 3.38526 15.4922 3.46117 16.0465C3.49663 16.3054 3.69407 16.5029 3.95301 16.5383C4.50728 16.6143 5.03969 16.6663 5.4165 16.6663C5.79332 16.6663 6.32573 16.6143 6.87999 16.5383C7.13893 16.5029 7.33638 16.3054 7.37184 16.0465C7.44775 15.4922 7.49984 14.9598 7.49984 14.583C7.49984 14.2062 7.44775 13.6738 7.37184 13.1195C7.33638 12.8606 7.13893 12.6631 6.87999 12.6277C6.32573 12.5518 5.79332 12.4997 5.4165 12.4997ZM3.72687 10.9764C2.72695 11.1134 1.94686 11.8935 1.80991 12.8934C1.73137 13.4669 1.6665 14.0898 1.6665 14.583C1.6665 15.0762 1.73137 15.6991 1.80991 16.2726C1.94686 17.2726 2.72695 18.0527 3.72687 18.1896C4.30037 18.2681 4.9233 18.333 5.4165 18.333C5.9097 18.333 6.53263 18.2681 7.10614 18.1896C8.10606 18.0527 8.88615 17.2726 9.02309 16.2726C9.10164 15.6991 9.1665 15.0762 9.1665 14.583C9.1665 14.0898 9.10164 13.4669 9.02309 12.8934C8.88615 11.8935 8.10606 11.1134 7.10614 10.9764C6.53263 10.8979 5.9097 10.833 5.4165 10.833C4.9233 10.833 4.30037 10.8979 3.72687 10.9764Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.5835 3.33366C14.2067 3.33366 13.6743 3.38575 13.12 3.46165C12.8611 3.49712 12.6636 3.69456 12.6282 3.9535C12.5523 4.50776 12.5002 5.04017 12.5002 5.41699C12.5002 5.79381 12.5523 6.32622 12.6282 6.88048C12.6636 7.13942 12.8611 7.33687 13.12 7.37233C13.6743 7.44824 14.2067 7.50033 14.5835 7.50033C14.9603 7.50033 15.4927 7.44824 16.047 7.37233C16.3059 7.33687 16.5034 7.13942 16.5388 6.88048C16.6147 6.32622 16.6668 5.79381 16.6668 5.41699C16.6668 5.04017 16.6147 4.50776 16.5388 3.9535C16.5034 3.69456 16.3059 3.49712 16.047 3.46165C15.4927 3.38575 14.9603 3.33366 14.5835 3.33366ZM12.8939 1.8104C11.8939 1.94734 11.1138 2.72744 10.9769 3.72736C10.8984 4.30086 10.8335 4.92379 10.8335 5.41699C10.8335 5.91019 10.8984 6.53312 10.9769 7.10663C11.1138 8.10655 11.8939 8.88664 12.8939 9.02358C13.4674 9.10213 14.0903 9.16699 14.5835 9.16699C15.0767 9.16699 15.6996 9.10213 16.2731 9.02358C17.2731 8.88664 18.0531 8.10655 18.1901 7.10663C18.2686 6.53312 18.3335 5.91019 18.3335 5.41699C18.3335 4.92379 18.2686 4.30086 18.1901 3.72736C18.0531 2.72744 17.2731 1.94734 16.2731 1.8104C15.6996 1.73186 15.0767 1.66699 14.5835 1.66699C14.0903 1.66699 13.4674 1.73186 12.8939 1.8104Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.4165 3.33366C5.03969 3.33366 4.50728 3.38575 3.95301 3.46165C3.69407 3.49712 3.49663 3.69456 3.46117 3.9535C3.38526 4.50776 3.33317 5.04017 3.33317 5.41699C3.33317 5.79381 3.38526 6.32622 3.46117 6.88048C3.49663 7.13942 3.69407 7.33687 3.95301 7.37233C4.50728 7.44824 5.03969 7.50033 5.4165 7.50033C5.79332 7.50033 6.32573 7.44824 6.87999 7.37233C7.13893 7.33687 7.33638 7.13942 7.37184 6.88048C7.44775 6.32622 7.49984 5.79381 7.49984 5.41699C7.49984 5.04017 7.44775 4.50776 7.37184 3.9535C7.33638 3.69456 7.13893 3.49712 6.87999 3.46165C6.32573 3.38575 5.79332 3.33366 5.4165 3.33366ZM3.72687 1.8104C2.72695 1.94734 1.94686 2.72744 1.80991 3.72736C1.73137 4.30086 1.6665 4.92379 1.6665 5.41699C1.6665 5.91019 1.73137 6.53312 1.80991 7.10663C1.94686 8.10655 2.72695 8.88664 3.72687 9.02358C4.30037 9.10213 4.9233 9.16699 5.4165 9.16699C5.9097 9.16699 6.53263 9.10213 7.10614 9.02358C8.10606 8.88664 8.88615 8.10655 9.02309 7.10663C9.10164 6.53312 9.1665 5.91019 9.1665 5.41699C9.1665 4.92379 9.10164 4.30086 9.02309 3.72736C8.88615 2.72744 8.10606 1.94734 7.10614 1.8104C6.53263 1.73186 5.9097 1.66699 5.4165 1.66699C4.9233 1.66699 4.30037 1.73186 3.72687 1.8104Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Grid;

<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('SEO Page Management') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Page SEO Management</h1>
                        <p class="mt-2 text-gray-600">Manage SEO settings for individual pages across the AutoLux website</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('marketing.seo.index') }}" 
                           class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to SEO Dashboard
                        </a>
                        <a href="{{ route('marketing.seo.keywords') }}" 
                           class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            Manage Keywords
                        </a>
                    </div>
                </div>
            </div>

            <!-- Pages List -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Website Pages</h3>
                    <p class="text-sm text-gray-600">Click on any page to edit its SEO settings</p>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Page Name
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    URL Pattern
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    SEO Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Last Updated
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($pages as $page)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                <i class="fas fa-file-alt text-blue-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $page['name'] }}</div>
                                            <div class="text-sm text-gray-500">{{ $page['id'] }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                                        {{ $page['url'] }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($page['status'] === 'active')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                                            Optimized
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <div class="w-2 h-2 bg-yellow-400 rounded-full mr-1"></div>
                                            Needs Work
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ now()->subDays(rand(1, 30))->format('M j, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('marketing.seo.pages.edit', $page['id']) }}" 
                                           class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded transition-colors">
                                            <i class="fas fa-edit mr-1"></i>
                                            Edit SEO
                                        </a>
                                        <a href="{{ $page['url'] === '/' ? route('public.home') : $page['url'] }}" 
                                           target="_blank"
                                           class="text-gray-600 hover:text-gray-900 bg-gray-50 hover:bg-gray-100 px-3 py-1 rounded transition-colors">
                                            <i class="fas fa-external-link-alt mr-1"></i>
                                            View Page
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- SEO Tips -->
            <div class="mt-8 bg-blue-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>
                    SEO Best Practices
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Title Tags</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Keep titles under 60 characters</li>
                            <li>• Include primary keyword near the beginning</li>
                            <li>• Make each title unique and descriptive</li>
                        </ul>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Meta Descriptions</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Keep descriptions under 160 characters</li>
                            <li>• Include a clear call-to-action</li>
                            <li>• Summarize page content accurately</li>
                        </ul>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Keywords</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Focus on 1-2 primary keywords per page</li>
                            <li>• Use keywords naturally in content</li>
                            <li>• Research competitor keywords</li>
                        </ul>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-blue-800">Content</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Create unique, valuable content</li>
                            <li>• Use header tags (H1, H2, H3) properly</li>
                            <li>• Optimize images with alt text</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

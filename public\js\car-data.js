/**
 * Car makes and models data 
 */
const carData = {
    makes: [
        'Toyota',
        'Volkswagen',
        'Ford',
        'Hyundai',
        'Nissan',
        'Mercedes-Benz',
        'BMW',
        'Kia',
        'Renault',
        'Suzuki',
        'Honda',
        'Audi',
        'Mazda',
        'Isuzu',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        'Opel',
        'Mitsubishi',
        'Datsun',
        'Lexus',
        'Volvo',
        'Jeep',
        'Land Rover',
        'Chery',
        'Peugeot',
        'Subaru',
        'Fiat',
        'Mini',
        'Jaguar',
        'Porsche'
    ],

    // Models by make
    models: {
        'Toyota': [
            'Corolla',
            'Hilux',
            'Fortuner',
            'RAV4',
            'Starlet',
            'Urban Cruiser',
            'Corolla Cross',
            'Corolla Quest',
            'Land Cruiser',
            'Quantum',
            'Hiace',
            'Avanza',
            'Rush',
            'Agya',
            'Etios',
            'Rumion',
            'Prado',
            'Yaris',
            'C-HR',
            'Supra'
        ],
        'Volkswagen': [
            'Polo',
            'Polo Vivo',
            'Golf',
            'Tiguan',
            'T-<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Transporter',
            '<PERSON><PERSON>reg',
            '<PERSON><PERSON><PERSON>',
            'Jetta',
            'Arteon',
            'T-Roc',
            'Passat',
            'Taigo',
            'ID.4'
        ],
        'Ford': [
            'Ranger',
            'EcoSport',
            'Figo',
            'Everest',
            'Fiesta',
            'Mustang',
            'Territory',
            'Kuga',
            'Focus',
            'Transit',
            'Tourneo',
            'Puma',
            'Bronco',
            'Raptor'
        ],
        'Hyundai': [
            'Grand i10',
            'i20',
            'Tucson',
            'Venue',
            'Creta',
            'Santa Fe',
            'H1',
            'Atos',
            'Kona',
            'Accent',
            'Elantra',
            'Palisade',
            'Staria',
            'Ioniq'
        ],
        'Nissan': [
            'NP200',
            'Navara',
            'Magnite',
            'X-Trail',
            'Almera',
            'Qashqai',
            'NP300 Hardbody',
            'Patrol',
            'Micra',
            'Leaf',
            'GT-R',
            'Terra',
            'Kicks'
        ],
        'Mercedes-Benz': [
            'C-Class',
            'A-Class',
            'GLC',
            'E-Class',
            'GLE',
            'GLA',
            'Sprinter',
            'V-Class',
            'CLA',
            'GLB',
            'S-Class',
            'G-Class',
            'GLS',
            'AMG GT',
            'EQA',
            'EQC',
            'Vito'
        ],
        'BMW': [
            '3 Series',
            'X3',
            'X5',
            '1 Series',
            'X1',
            '5 Series',
            'X7',
            'X4',
            'X6',
            '2 Series',
            '4 Series',
            '7 Series',
            'Z4',
            'i3',
            'i4',
            'iX',
            'M3',
            'M4',
            'M5'
        ],
        'Kia': [
            'Picanto',
            'Rio',
            'Seltos',
            'Sportage',
            'Sonet',
            'Sorento',
            'Carnival',
            'Pegas',
            'K2700',
            'Grand Sedona',
            'Cerato',
            'Soul',
            'Stinger'
        ],
        'Renault': [
            'Kwid',
            'Triber',
            'Kiger',
            'Duster',
            'Clio',
            'Sandero',
            'Koleos',
            'Captur',
            'Megane',
            'Kadjar',
            'Kangoo',
            'Trafic'
        ],
        'Suzuki': [
            'Alto',
            'Swift',
            'S-Presso',
            'Jimny',
            'Vitara Brezza',
            'Baleno',
            'Ignis',
            'Ertiga',
            'Dzire',
            'Ciaz',
            'Grand Vitara',
            'Super Carry'
        ],
        'Honda': [
            'Amaze',
            'Jazz',
            'Civic',
            'HR-V',
            'CR-V',
            'Accord',
            'BR-V',
            'WR-V',
            'Ballade',
            'Brio'
        ],
        'Audi': [
            'A3',
            'A4',
            'A5',
            'A6',
            'A7',
            'A8',
            'Q2',
            'Q3',
            'Q5',
            'Q7',
            'Q8',
            'e-tron',
            'TT',
            'R8',
            'S3',
            'S4',
            'RS3',
            'RS4',
            'RS6'
        ],
        'Mazda': [
            'CX-3',
            'CX-5',
            'CX-30',
            'Mazda2',
            'Mazda3',
            'BT-50',
            'MX-5',
            'CX-9'
        ],
        'Isuzu': [
            'D-Max',
            'MU-X',
            'KB',
            'N-Series',
            'F-Series'
        ],
        'Haval': [
            'H1',
            'H2',
            'H6',
            'Jolion',
            'H9',
            'Big Dog'
        ],
        'Mahindra': [
            'KUV100',
            'XUV300',
            'XUV500',
            'Scorpio',
            'Pik Up',
            'TUV300',
            'Bolero'
        ],
        'Opel': [
            'Corsa',
            'Astra',
            'Crossland',
            'Grandland',
            'Mokka',
            'Combo',
            'Zafira'
        ],
        'Mitsubishi': [
            'Pajero Sport',
            'ASX',
            'Eclipse Cross',
            'Outlander',
            'Triton',
            'Xpander',
            'Pajero'
        ],
        'Datsun': [
            'GO',
            'GO+',
            'redi-GO'
        ],
        'Lexus': [
            'UX',
            'NX',
            'RX',
            'LX',
            'ES',
            'IS',
            'LS',
            'LC',
            'RC'
        ]
    }
};

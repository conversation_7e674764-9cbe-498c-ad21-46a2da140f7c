/**
 * Kinetic Typography Component
 * Handles animated text effects with various animation types
 */
class KineticText {
    constructor(element, options = {}) {
        this.element = typeof element === 'string' ? document.querySelector(element) : element;
        this.options = {
            animationType: 'reveal', // reveal, typewriter, morphing, floating, glitch
            duration: 1000,
            delay: 0,
            easing: 'ease-out',
            triggerOnScroll: true,
            triggerOffset: 0.8, // Trigger when 80% visible
            repeat: false,
            splitBy: 'chars', // chars, words, lines
            stagger: 50, // Delay between each element
            ...options
        };

        this.isAnimated = false;
        this.observer = null;
        this.elements = [];
        this.animationId = null;

        if (this.element) {
            this.init();
        }
    }

    init() {
        this.prepareText();
        
        if (this.options.triggerOnScroll) {
            this.setupScrollTrigger();
        } else {
            setTimeout(() => this.animate(), this.options.delay);
        }
    }

    prepareText() {
        const text = this.element.textContent;
        this.element.innerHTML = '';
        
        switch (this.options.splitBy) {
            case 'chars':
                this.splitByCharacters(text);
                break;
            case 'words':
                this.splitByWords(text);
                break;
            case 'lines':
                this.splitByLines(text);
                break;
            default:
                this.splitByCharacters(text);
        }

        // Apply initial styles based on animation type
        this.applyInitialStyles();
    }

    splitByCharacters(text) {
        [...text].forEach((char, index) => {
            const span = document.createElement('span');
            span.textContent = char === ' ' ? '\u00A0' : char; // Non-breaking space
            span.style.display = 'inline-block';
            span.style.animationDelay = `${index * this.options.stagger}ms`;
            span.dataset.index = index;
            this.elements.push(span);
            this.element.appendChild(span);
        });
    }

    splitByWords(text) {
        text.split(' ').forEach((word, index) => {
            const span = document.createElement('span');
            span.textContent = word;
            span.style.display = 'inline-block';
            span.style.marginRight = '0.25em';
            span.style.animationDelay = `${index * this.options.stagger}ms`;
            span.dataset.index = index;
            this.elements.push(span);
            this.element.appendChild(span);
        });
    }

    splitByLines(text) {
        // Simple line splitting - in a real implementation, you'd measure text width
        const words = text.split(' ');
        const wordsPerLine = Math.ceil(words.length / 3); // Rough estimate
        
        for (let i = 0; i < words.length; i += wordsPerLine) {
            const lineWords = words.slice(i, i + wordsPerLine);
            const span = document.createElement('span');
            span.textContent = lineWords.join(' ');
            span.style.display = 'block';
            span.style.animationDelay = `${(i / wordsPerLine) * this.options.stagger}ms`;
            span.dataset.index = i / wordsPerLine;
            this.elements.push(span);
            this.element.appendChild(span);
        }
    }

    applyInitialStyles() {
        this.elements.forEach(element => {
            switch (this.options.animationType) {
                case 'reveal':
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(20px)';
                    break;
                case 'typewriter':
                    element.style.opacity = '0';
                    break;
                case 'morphing':
                    element.style.opacity = '0';
                    element.style.transform = 'scale(0.5) rotate(180deg)';
                    break;
                case 'floating':
                    element.style.transform = 'translateY(0px)';
                    break;
                case 'glitch':
                    element.style.position = 'relative';
                    break;
            }
        });
    }

    setupScrollTrigger() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.intersectionRatio >= this.options.triggerOffset) {
                        this.animate();
                        if (!this.options.repeat) {
                            this.observer.unobserve(this.element);
                        }
                    }
                });
            }, {
                threshold: this.options.triggerOffset
            });

            this.observer.observe(this.element);
        } else {
            // Fallback for browsers without IntersectionObserver
            window.addEventListener('scroll', this.checkScrollPosition.bind(this));
        }
    }

    checkScrollPosition() {
        const rect = this.element.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        
        if (rect.top < windowHeight * this.options.triggerOffset) {
            this.animate();
            if (!this.options.repeat) {
                window.removeEventListener('scroll', this.checkScrollPosition.bind(this));
            }
        }
    }

    animate() {
        if (this.isAnimated && !this.options.repeat) return;
        
        this.isAnimated = true;

        switch (this.options.animationType) {
            case 'reveal':
                this.animateReveal();
                break;
            case 'typewriter':
                this.animateTypewriter();
                break;
            case 'morphing':
                this.animateMorphing();
                break;
            case 'floating':
                this.animateFloating();
                break;
            case 'glitch':
                this.animateGlitch();
                break;
        }
    }

    animateReveal() {
        this.elements.forEach((element, index) => {
            setTimeout(() => {
                element.style.transition = `opacity ${this.options.duration}ms ${this.options.easing}, transform ${this.options.duration}ms ${this.options.easing}`;
                element.style.opacity = '1';
                element.style.transform = 'translateY(0px)';
            }, index * this.options.stagger);
        });
    }

    animateTypewriter() {
        let currentIndex = 0;
        
        const typeNextChar = () => {
            if (currentIndex < this.elements.length) {
                this.elements[currentIndex].style.opacity = '1';
                currentIndex++;
                setTimeout(typeNextChar, this.options.stagger);
            }
        };

        typeNextChar();
    }

    animateMorphing() {
        this.elements.forEach((element, index) => {
            setTimeout(() => {
                element.style.transition = `all ${this.options.duration}ms ${this.options.easing}`;
                element.style.opacity = '1';
                element.style.transform = 'scale(1) rotate(0deg)';
            }, index * this.options.stagger);
        });
    }

    animateFloating() {
        this.elements.forEach((element, index) => {
            const floatHeight = Math.random() * 10 + 5; // Random float between 5-15px
            const duration = Math.random() * 2000 + 2000; // Random duration between 2-4s
            
            element.style.animation = `float-${index} ${duration}ms ease-in-out infinite alternate`;
            
            // Create unique keyframes for each element
            const keyframes = `
                @keyframes float-${index} {
                    0% { transform: translateY(0px); }
                    100% { transform: translateY(-${floatHeight}px); }
                }
            `;
            
            if (!document.getElementById(`float-keyframes-${index}`)) {
                const style = document.createElement('style');
                style.id = `float-keyframes-${index}`;
                style.textContent = keyframes;
                document.head.appendChild(style);
            }
        });
    }

    animateGlitch() {
        this.elements.forEach((element, index) => {
            setTimeout(() => {
                this.createGlitchEffect(element);
            }, index * this.options.stagger);
        });
    }

    createGlitchEffect(element) {
        const originalText = element.textContent;
        const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        let glitchCount = 0;
        const maxGlitches = 5;

        const glitchInterval = setInterval(() => {
            if (glitchCount < maxGlitches) {
                // Random glitch character
                const randomChar = glitchChars[Math.floor(Math.random() * glitchChars.length)];
                element.textContent = Math.random() > 0.5 ? randomChar : originalText;
                
                // Random position offset
                element.style.transform = `translate(${Math.random() * 4 - 2}px, ${Math.random() * 4 - 2}px)`;
                
                // Random color
                const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
                element.style.color = colors[Math.floor(Math.random() * colors.length)];
                
                glitchCount++;
            } else {
                // Reset to original
                element.textContent = originalText;
                element.style.transform = 'translate(0, 0)';
                element.style.color = '';
                clearInterval(glitchInterval);
            }
        }, 100);
    }

    // Public methods
    reset() {
        this.isAnimated = false;
        this.applyInitialStyles();
    }

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        // Remove event listeners
        window.removeEventListener('scroll', this.checkScrollPosition.bind(this));
    }

    // Static method to initialize all kinetic text elements on page
    static initAll(selector = '[data-kinetic-text]') {
        const elements = document.querySelectorAll(selector);
        const instances = [];

        elements.forEach(element => {
            const options = {
                animationType: element.dataset.animationType || 'reveal',
                duration: parseInt(element.dataset.duration) || 1000,
                delay: parseInt(element.dataset.delay) || 0,
                splitBy: element.dataset.splitBy || 'chars',
                stagger: parseInt(element.dataset.stagger) || 50,
                triggerOnScroll: element.dataset.triggerOnScroll !== 'false',
                repeat: element.dataset.repeat === 'true'
            };

            const instance = new KineticText(element, options);
            instances.push(instance);
        });

        return instances;
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    KineticText.initAll();
});

// Export for use in other scripts
window.KineticText = KineticText;

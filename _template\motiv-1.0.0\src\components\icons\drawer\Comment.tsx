import { SvgIcon, SvgIconProps } from '@mui/material';

const Comment = (props: SvgIconProps) => {
  return (
    <SvgIcon width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.83317 13.2514V15.8296L10.0517 13.3368L10.4926 13.3296C12.2459 13.3009 13.8237 13.1244 15.0035 12.9449C15.7481 12.8316 16.2654 12.3119 16.3778 11.6594C16.5337 10.7544 16.6665 9.60776 16.6665 8.33366C16.6665 7.05956 16.5337 5.91293 16.3778 5.00788C16.2654 4.35539 15.7481 3.83576 15.0035 3.72247C13.7131 3.52613 11.9484 3.33366 9.99984 3.33366C8.05123 3.33366 6.28657 3.52613 4.99619 3.72247C4.25162 3.83576 3.73425 4.3554 3.62186 5.00788C3.46597 5.91293 3.33317 7.05956 3.33317 8.33366C3.33317 9.60776 3.46597 10.7544 3.62186 11.6594C3.71946 12.226 4.11529 12.6802 4.6924 12.8721L5.83317 13.2514ZM1.97939 4.72497C2.22121 3.32106 3.33711 2.28906 4.74548 2.07476C6.09493 1.86944 7.94616 1.66699 9.99984 1.66699C12.0535 1.66699 13.9047 1.86944 15.2542 2.07476C16.6626 2.28906 17.7785 3.32105 18.0203 4.72497C18.1888 5.70342 18.3332 6.94637 18.3332 8.33366C18.3332 9.72095 18.1888 10.9639 18.0203 11.9424C17.7785 13.3463 16.6626 14.3783 15.2542 14.5926C14.0197 14.7804 12.3652 14.9658 10.5199 14.996L5.42378 18.0074C4.86826 18.3356 4.1665 17.9352 4.1665 17.29V14.4536C3.03864 14.0786 2.18697 13.1475 1.97939 11.9424C1.81085 10.9639 1.6665 9.72095 1.6665 8.33366C1.6665 6.94637 1.81085 5.70342 1.97939 4.72497Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.83333 5.83301C5.3731 5.83301 5 6.2061 5 6.66634C5 7.12658 5.3731 7.49967 5.83333 7.49967H14.1667C14.6269 7.49967 15 7.12658 15 6.66634C15 6.2061 14.6269 5.83301 14.1667 5.83301H5.83333ZM5.83333 9.16634C5.3731 9.16634 5 9.53944 5 9.99968C5 10.4599 5.3731 10.833 5.83333 10.833H9.16667C9.6269 10.833 10 10.4599 10 9.99968C10 9.53944 9.6269 9.16634 9.16667 9.16634H5.83333Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Comment;

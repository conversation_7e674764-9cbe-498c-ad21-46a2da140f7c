<!-- Top Navigation Bar -->
<header class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Left side - Mobile menu button and breadcrumbs -->
            <div class="flex items-center">
                <!-- Mobile menu button -->
                <button @click="sidebarOpen = true"
                        class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                    <span class="sr-only">Open sidebar</span>
                    <i class="fas fa-bars text-lg"></i>
                </button>

                <!-- Breadcrumbs for larger screens -->
                <nav class="hidden lg:flex items-center space-x-4 ml-4" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <div>
                                <a href="{{ route('dashboard') }}" class="text-gray-400 hover:text-gray-500">
                                    <i class="fas fa-home text-sm"></i>
                                    <span class="sr-only">Home</span>
                                </a>
                            </div>
                        </li>
                        @if(!request()->routeIs('dashboard'))
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                                <span class="ml-4 text-sm font-medium text-gray-500">
                                    @if(request()->routeIs('cars.*'))
                                        Cars
                                    @elseif(request()->routeIs('parts.*'))
                                        Parts
                                    @elseif(request()->routeIs('labor.*'))
                                        Labor
                                    @elseif(request()->routeIs('painting.*'))
                                        Painting
                                    @elseif(request()->routeIs('sales.*'))
                                        Sales
                                    @elseif(request()->routeIs('reports.*'))
                                        Reports
                                    @elseif(request()->routeIs('suppliers.*'))
                                        Suppliers
                                    @elseif(request()->routeIs('customers.*'))
                                        Customers
                                    @elseif(request()->routeIs('documents.*'))
                                        Documents
                                    @elseif(request()->routeIs('users.*'))
                                        Users
                                    @elseif(request()->routeIs('api-integration.*'))
                                        API Integration
                                    @elseif(request()->routeIs('marketing.*'))
                                        Marketing
                                    @elseif(request()->routeIs('activity-logs.*'))
                                        Activity Logs
                                    @else
                                        {{ ucfirst(request()->segment(1)) }}
                                    @endif
                                </span>
                            </div>
                        </li>
                        @endif
                    </ol>
                </nav>
            </div>

            <!-- Right side - Search, notifications, and user menu -->
            <div class="flex items-center space-x-4">
                <!-- Search (hidden on mobile) -->
                <div class="hidden md:block">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 text-sm"></i>
                        </div>
                        <input type="text"
                               placeholder="Search cars, parts..."
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>

                <!-- Notifications -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open"
                            class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="sr-only">View notifications</span>
                        <i class="fas fa-bell text-lg"></i>
                        <!-- Notification badge -->
                        <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                    </button>

                    <!-- Notifications dropdown -->
                    <div x-show="open"
                         @click.away="open = false"
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         class="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                         style="display: none;">
                        <div class="py-1">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <h3 class="text-sm font-medium text-gray-900">Notifications</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-car text-blue-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900">New car added</p>
                                            <p class="text-sm text-gray-500">2020 BMW X5 has been added to inventory</p>
                                            <p class="text-xs text-gray-400 mt-1">2 hours ago</p>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-wrench text-yellow-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900">Repair completed</p>
                                            <p class="text-sm text-gray-500">Mercedes C-Class repair work finished</p>
                                            <p class="text-xs text-gray-400 mt-1">4 hours ago</p>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-handshake text-green-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900">Sale completed</p>
                                            <p class="text-sm text-gray-500">Audi A4 sold for R450,000</p>
                                            <p class="text-xs text-gray-400 mt-1">1 day ago</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="border-t border-gray-200">
                                <a href="#" class="block px-4 py-2 text-sm text-center text-blue-600 hover:text-blue-500">
                                    View all notifications
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User menu -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open"
                            class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="sr-only">Open user menu</span>
                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-sm font-medium text-white">
                                {{ substr(Auth::user()->name, 0, 1) }}
                            </span>
                        </div>
                        <span class="hidden md:ml-3 md:block text-gray-700 text-sm font-medium">
                            {{ Auth::user()->name }}
                        </span>
                        <i class="hidden md:block ml-2 fas fa-chevron-down text-gray-400 text-xs"></i>
                    </button>

                    <!-- User dropdown -->
                    <div x-show="open"
                         @click.away="open = false"
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                         style="display: none;">
                        <div class="py-1">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">{{ Auth::user()->name }}</p>
                                <p class="text-sm text-gray-500">{{ Auth::user()->email }}</p>
                                @if(Auth::user()->isSuperuser())
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 mt-1">
                                        Superuser
                                    </span>
                                @elseif(Auth::user()->isAdmin())
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                        Admin
                                    </span>
                                @endif
                            </div>
                            <a href="#"
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-user mr-2"></i>
                                Your Profile
                            </a>
                            <a href="#"
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-cog mr-2"></i>
                                Settings
                            </a>
                            <div class="border-t border-gray-200"></div>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit"
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    Sign out
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\HasAuthorization;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Car extends Model
{
    use HasFactory, SoftDeletes, HasAuthorization, Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'created_by',
        'updated_by',
        'slug',
        'make',
        'model',
        'variant',
        'year',
        'vin',
        'registration_number',
        'color',
        'interior_type',
        'body_type',
        'engine_size',
        'fuel_type',
        'transmission',
        'mileage',
        'features',
        'purchase_date',
        'purchase_price',
        'purchase_finance_type',
        'finance_institution',
        'finance_amount',
        'finance_interest_rate',
        'finance_term_months',
        'monthly_payment',
        'finance_start_date',
        'finance_end_date',
        'finance_notes',
        'current_location',
        'previous_location',
        'location_changed_date',
        'location_notes',
        'auction_house',
        'auction_branch',
        'auction_lot_number',
        'damage_description',
        'damage_severity',
        'operational_status',
        'vehicle_code',
        'current_phase',
        'repair_start_date',
        'repair_end_date',
        'dealership_date',
        'sold_date',
        'transportation_cost',
        'registration_papers_cost',
        'number_plates_cost',
        'dealership_discount',
        'other_costs',
        'other_costs_description',
        'estimated_repair_cost',
        'estimated_market_value',
        'notes',
        'form_completed',
        'form_step',
        'status',
        // Public listing fields
        'public_listing',
        'featured',
        'listing_priority',
        'meta_title',
        'meta_description',
        'seo_keywords',
        'public_description',
        'key_features',
        'view_count',
        'inquiry_count',
        // User posting fields
        'user_posted',
        'contact_phone',
        'location',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'features' => 'array',
        'purchase_date' => 'date',
        'repair_start_date' => 'date',
        'repair_end_date' => 'date',
        'dealership_date' => 'date',
        'sold_date' => 'date',
        'finance_start_date' => 'date',
        'finance_end_date' => 'date',
        'location_changed_date' => 'date',
        'purchase_price' => 'decimal:2',
        'finance_amount' => 'decimal:2',
        'finance_interest_rate' => 'decimal:2',
        'monthly_payment' => 'decimal:2',
        'transportation_cost' => 'decimal:2',
        'registration_papers_cost' => 'decimal:2',
        'number_plates_cost' => 'decimal:2',
        'dealership_discount' => 'decimal:2',
        'other_costs' => 'decimal:2',
        'estimated_repair_cost' => 'decimal:2',
        'estimated_market_value' => 'decimal:2',
        'form_completed' => 'boolean',
        'form_step' => 'integer',
        'deleted_at' => 'datetime',
        // Public listing casts
        'public_listing' => 'boolean',
        'featured' => 'boolean',
        'listing_priority' => 'integer',
        'key_features' => 'array',
        'view_count' => 'integer',
        'inquiry_count' => 'integer',
        // User posting casts
        'user_posted' => 'boolean',
    ];

    /**
     * Get the user that owns the car.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the images for the car.
     */
    public function images()
    {
        return $this->morphMany(Image::class, 'imageable');
    }

    /**
     * Get the damaged parts for the car.
     */
    public function damagedParts()
    {
        return $this->hasMany(DamagedPart::class);
    }

    /**
     * Get the parts for the car.
     */
    public function parts()
    {
        return $this->hasMany(Part::class);
    }

    /**
     * Get the labor entries for the car.
     */
    public function laborEntries()
    {
        return $this->hasMany(Labor::class);
    }

    /**
     * Get the painting entries for the car.
     */
    public function paintingEntries()
    {
        return $this->hasMany(Painting::class);
    }

    /**
     * Get the sale record for the car.
     */
    public function sale()
    {
        return $this->hasOne(Sale::class);
    }

    /**
     * Get the documents for the car.
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the car images for public display.
     */
    public function carImages()
    {
        return $this->hasMany(CarImage::class)->ordered();
    }

    /**
     * Get the primary car image.
     */
    public function primaryImage()
    {
        return $this->hasOne(CarImage::class)->where('is_primary', true);
    }

    /**
     * Get car features.
     */
    public function features()
    {
        return $this->belongsToMany(CarFeature::class, 'car_car_feature');
    }

    /**
     * Get public inquiries for this car.
     */
    public function publicInquiries()
    {
        return $this->hasMany(PublicInquiry::class);
    }

    /**
     * Calculate the total investment in the car.
     */
    public function getTotalInvestmentAttribute()
    {
        $partsCost = $this->parts()->sum('total_price');
        $laborCost = $this->laborEntries()->sum('total_cost');
        $paintingCost = $this->paintingEntries()->sum('total_cost');

        $totalInvestment = $this->purchase_price +
               $this->transportation_cost +
               $this->registration_papers_cost +
               $this->number_plates_cost +
               $this->other_costs +
               $partsCost +
               $laborCost +
               $paintingCost;

        // Dealership discount is subtracted from the total investment
        if ($this->dealership_discount) {
            $totalInvestment -= $this->dealership_discount;
        }

        return $totalInvestment;
    }

    /**
     * Calculate the actual profit or loss on the car (if sold).
     */
    public function getProfitLossAttribute()
    {
        if (!$this->sale || !$this->sale->selling_price) {
            return null;
        }

        return $this->sale->selling_price - $this->getTotalInvestmentAttribute();
    }

    /**
     * Calculate the projected profit or loss on the car based on estimated market value.
     */
    public function getProjectedProfitLossAttribute()
    {
        if (!$this->estimated_market_value) {
            return null;
        }

        return $this->estimated_market_value - $this->getTotalInvestmentAttribute();
    }

    /**
     * Calculate the effective selling price after applying dealership discount.
     */
    public function getEffectiveSellingPriceAttribute()
    {
        if (!$this->sale || !$this->sale->selling_price) {
            return $this->estimated_market_value;
        }

        $sellingPrice = $this->sale->selling_price;

        if ($this->dealership_discount) {
            $sellingPrice -= $this->dealership_discount;
        }

        return $sellingPrice;
    }

    /**
     * Calculate the actual ROI percentage (if sold).
     */
    public function getRoiPercentageAttribute()
    {
        if (!$this->sale || !$this->sale->selling_price || $this->getTotalInvestmentAttribute() == 0) {
            return null;
        }

        return ($this->getProfitLossAttribute() / $this->getTotalInvestmentAttribute()) * 100;
    }

    /**
     * Calculate the projected ROI percentage based on estimated market value.
     */
    public function getProjectedRoiPercentageAttribute()
    {
        if (!$this->estimated_market_value || $this->getTotalInvestmentAttribute() == 0) {
            return null;
        }

        return ($this->getProjectedProfitLossAttribute() / $this->getTotalInvestmentAttribute()) * 100;
    }

    /**
     * Calculate the days in repair.
     */
    public function getDaysInRepairAttribute()
    {
        if (!$this->repair_start_date) {
            return null;
        }

        $endDate = $this->repair_end_date ?? now();
        return $this->repair_start_date->diffInDays($endDate);
    }

    /**
     * Calculate the days at dealership.
     */
    public function getDaysAtDealershipAttribute()
    {
        if (!$this->dealership_date) {
            return null;
        }

        $endDate = $this->sold_date ?? now();
        return $this->dealership_date->diffInDays($endDate);
    }

    /**
     * Calculate the total days from purchase to sale or current date.
     */
    public function getTotalDaysAttribute()
    {
        if (!$this->purchase_date) {
            return null;
        }

        $endDate = $this->sold_date ?? now();
        return $this->purchase_date->diffInDays($endDate);
    }

    /**
     * Scope to get publicly listed cars
     */
    public function scopePublicListing($query)
    {
        return $query->where('public_listing', true);
    }

    /**
     * Scope to get featured cars
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true)->where('public_listing', true);
    }

    /**
     * Scope to order by listing priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('listing_priority', 'desc')->orderBy('created_at', 'desc');
    }

    /**
     * Scope to search cars by make and model
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('make', 'like', "%{$search}%")
              ->orWhere('model', 'like', "%{$search}%")
              ->orWhere('year', 'like', "%{$search}%");
        });
    }

    /**
     * Increment view count
     */
    public function incrementViewCount()
    {
        $this->increment('view_count');
    }

    /**
     * Increment inquiry count
     */
    public function incrementInquiryCount()
    {
        $this->increment('inquiry_count');
    }



    /**
     * Get the primary image URL or a default
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        if ($this->carImages->count() > 0) {
            return $this->carImages->first()->url;
        }

        return asset('images/car-placeholder.jpg');
    }

    /**
     * Get the car's display name
     */
    public function getDisplayNameAttribute()
    {
        return "{$this->year} {$this->make} {$this->model}" . ($this->variant ? " {$this->variant}" : '');
    }

    /**
     * Get the investment potential score
     */
    public function getInvestmentScoreAttribute()
    {
        if (!$this->estimated_market_value || !$this->getTotalInvestmentAttribute()) {
            return null;
        }

        $projectedRoi = $this->getProjectedRoiPercentageAttribute();

        if ($projectedRoi === null) {
            return null;
        }

        // Score based on ROI percentage
        if ($projectedRoi >= 30) return 'Excellent';
        if ($projectedRoi >= 20) return 'Very Good';
        if ($projectedRoi >= 10) return 'Good';
        if ($projectedRoi >= 0) return 'Fair';
        return 'Poor';
    }

    /**
     * Check if car is available for public viewing
     */
    public function getIsAvailableAttribute()
    {
        return $this->public_listing &&
               in_array($this->current_phase, ['dealership', 'sold']) &&
               $this->status === 'active';
    }



    /**
     * Get the holding period in days for investment analysis
     */
    public function getHoldingPeriodDaysAttribute()
    {
        if (!$this->purchase_date) {
            return null;
        }

        $endDate = $this->sold_date ?? now();
        return $this->purchase_date->diffInDays($endDate);
    }

    /**
     * Get the investment score as a numeric value (0-100)
     */
    public function getInvestmentScoreNumericAttribute()
    {
        $projectedRoi = $this->getProjectedRoiPercentageAttribute();

        if ($projectedRoi === null) {
            return null;
        }

        // Convert ROI percentage to a score out of 100
        if ($projectedRoi >= 30) return 95;
        if ($projectedRoi >= 25) return 85;
        if ($projectedRoi >= 20) return 75;
        if ($projectedRoi >= 15) return 65;
        if ($projectedRoi >= 10) return 55;
        if ($projectedRoi >= 5) return 45;
        if ($projectedRoi >= 0) return 35;
        return 20;
    }

    /**
     * Generate a unique slug for the car
     */
    public function generateSlug()
    {
        $baseSlug = \Illuminate\Support\Str::slug($this->display_name);
        $slug = $baseSlug;
        $counter = 1;

        // Ensure uniqueness
        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the route key name for model binding
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Boot method to auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($car) {
            if (empty($car->slug)) {
                $car->slug = $car->generateSlug();
            }
        });

        static::updating(function ($car) {
            if ($car->isDirty(['make', 'model', 'variant', 'year']) && empty($car->slug)) {
                $car->slug = $car->generateSlug();
            }
        });
    }

    /**
     * Get the public URL for this car using slug
     */
    public function getPublicUrlAttribute()
    {
        return route('public.cars.show', $this->slug);
    }
}

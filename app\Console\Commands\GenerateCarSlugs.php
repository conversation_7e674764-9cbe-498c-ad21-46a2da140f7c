<?php

namespace App\Console\Commands;

use App\Models\Car;
use Illuminate\Console\Command;

class GenerateCarSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cars:generate-slugs {--force : Force regenerate all slugs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for cars that don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');

        if ($force) {
            $cars = Car::all();
            $this->info('Regenerating slugs for all cars...');
        } else {
            $cars = Car::whereNull('slug')->orWhere('slug', '')->get();
            $this->info('Generating slugs for cars without slugs...');
        }

        if ($cars->count() === 0) {
            $this->info('No cars need slug generation.');
            return;
        }

        $bar = $this->output->createProgressBar($cars->count());
        $bar->start();

        foreach ($cars as $car) {
            $car->slug = $car->generateSlug();
            $car->save();
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Successfully generated slugs for {$cars->count()} cars.");
    }
}

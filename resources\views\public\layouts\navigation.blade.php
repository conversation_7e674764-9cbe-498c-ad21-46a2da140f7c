<nav class="bg-white shadow-lg sticky top-0 z-30">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="{{ route('public.home') }}" class="flex items-center">
                    <img class="h-8 w-auto" src="{{ asset('images/logo.svg') }}" alt="AutoLux" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <span class="ml-2 text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent hidden">AutoLux</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-4">
                    <a href="{{ route('public.home') }}"
                       class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors {{ request()->routeIs('public.home') ? 'text-blue-600 bg-blue-50' : '' }}">
                        Home
                    </a>
                    <a href="{{ route('public.cars.index') }}"
                       class="text-gray-900 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors {{ request()->routeIs('public.cars.*') ? 'text-amber-600 bg-amber-50' : '' }}">
                        Luxury Collection
                    </a>
                    <a href="{{ route('public.search.index') }}"
                       class="text-gray-900 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors {{ request()->routeIs('public.search.*') ? 'text-amber-600 bg-amber-50' : '' }}">
                        Find Your Car
                    </a>
                    <a href="#"
                       class="text-gray-900 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        Premium Financing
                    </a>
                    <a href="{{ route('public.contact.index') }}"
                       class="text-gray-900 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors {{ request()->routeIs('public.contact.*') ? 'text-amber-600 bg-amber-50' : '' }}">
                        Contact
                    </a>
                </div>
            </div>

            <!-- Search and CTA -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Search Button -->
                <button data-search-trigger class="text-gray-500 hover:text-gray-700 p-2 rounded-md transition-colors">
                    <i class="fas fa-search"></i>
                </button>

                <!-- Admin Login -->
                @auth
                    <a href="{{ route('dashboard') }}"
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">
                        Dashboard
                    </a>
                @else
                    <a href="{{ route('login') }}"
                       class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        Admin Login
                    </a>
                @endauth

                <!-- CTA Button -->
                <a href="{{ route('public.cars.index') }}"
                   class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                    View Cars
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-700 p-2 rounded-md transition-colors">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="md:hidden hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
            <a href="{{ route('public.home') }}"
               class="text-gray-900 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium transition-colors {{ request()->routeIs('public.home') ? 'text-blue-600 bg-blue-50' : '' }}">
                Home
            </a>
            <a href="{{ route('public.cars.index') }}"
               class="text-gray-900 hover:text-amber-600 block px-3 py-2 rounded-md text-base font-medium transition-colors {{ request()->routeIs('public.cars.*') ? 'text-amber-600 bg-amber-50' : '' }}">
                Luxury Collection
            </a>
            <a href="{{ route('public.search.index') }}"
               class="text-gray-900 hover:text-amber-600 block px-3 py-2 rounded-md text-base font-medium transition-colors {{ request()->routeIs('public.search.*') ? 'text-amber-600 bg-amber-50' : '' }}">
                Find Your Car
            </a>
            <a href="#"
               class="text-gray-900 hover:text-amber-600 block px-3 py-2 rounded-md text-base font-medium transition-colors">
                Premium Financing
            </a>
            <a href="{{ route('public.contact.index') }}"
               class="text-gray-900 hover:text-amber-600 block px-3 py-2 rounded-md text-base font-medium transition-colors {{ request()->routeIs('public.contact.*') ? 'text-amber-600 bg-amber-50' : '' }}">
                Contact
            </a>

            <!-- Mobile Search -->
            <button data-search-trigger class="w-full text-left text-gray-900 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium transition-colors">
                <i class="fas fa-search mr-2"></i>Search Cars
            </button>

            <!-- Mobile Auth/CTA -->
            <div class="border-t border-gray-200 pt-3 mt-3">
                @auth
                    <a href="{{ route('dashboard') }}"
                       class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-colors">
                        Dashboard
                    </a>
                @else
                    <a href="{{ route('login') }}"
                       class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-colors">
                        Admin Login
                    </a>
                @endauth

                <a href="{{ route('public.cars.index') }}"
                   class="bg-blue-600 text-white block px-3 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors mt-2">
                    View Cars
                </a>
            </div>
        </div>
    </div>
</nav>

<!-- Breadcrumbs (if needed) -->
@if(isset($breadcrumbs) && count($breadcrumbs) > 0)
<nav class="bg-gray-50 border-b border-gray-200" aria-label="Breadcrumb">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center space-x-4 py-3">
            <ol class="flex items-center space-x-4">
                <li>
                    <a href="{{ route('public.home') }}" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Home</span>
                    </a>
                </li>
                @foreach($breadcrumbs as $breadcrumb)
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        @if(isset($breadcrumb['url']) && !$loop->last)
                            <a href="{{ $breadcrumb['url'] }}" class="text-gray-500 hover:text-gray-700">
                                {{ $breadcrumb['title'] }}
                            </a>
                        @else
                            <span class="text-gray-900 font-medium">{{ $breadcrumb['title'] }}</span>
                        @endif
                    </li>
                @endforeach
            </ol>
        </div>
    </div>
</nav>
@endif

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');

            // Update button icon
            const icon = mobileMenuButton.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.className = 'fas fa-bars';
            } else {
                icon.className = 'fas fa-times';
            }
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            if (!mobileMenu.contains(event.target) && !mobileMenuButton.contains(event.target)) {
                mobileMenu.classList.add('hidden');
                const icon = mobileMenuButton.querySelector('i');
                icon.className = 'fas fa-bars';
            }
        }
    });

    // Close mobile menu on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768 && mobileMenu && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
            const icon = mobileMenuButton.querySelector('i');
            icon.className = 'fas fa-bars';
        }
    });
});
</script>

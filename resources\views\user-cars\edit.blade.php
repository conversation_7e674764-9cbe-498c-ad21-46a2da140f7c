<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Car Listing') }}
        </h2>
    </x-slot>
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Edit Car Listing</h1>
            <p class="text-gray-600 mt-2">Update your car listing details</p>
        </div>

        @if($errors->any())
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('user-cars.update', $car) }}" method="POST" enctype="multipart/form-data" class="space-y-8">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="make" class="block text-sm font-medium text-gray-700 mb-2">Make *</label>
                        <input type="text" id="make" name="make" value="{{ old('make', $car->make) }}" required
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="model" class="block text-sm font-medium text-gray-700 mb-2">Model *</label>
                        <input type="text" id="model" name="model" value="{{ old('model', $car->model) }}" required
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="variant" class="block text-sm font-medium text-gray-700 mb-2">Variant</label>
                        <input type="text" id="variant" name="variant" value="{{ old('variant', $car->variant) }}"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Year *</label>
                        <input type="number" id="year" name="year" value="{{ old('year', $car->year) }}" required
                               min="1900" max="{{ date('Y') + 1 }}"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="mileage" class="block text-sm font-medium text-gray-700 mb-2">Mileage (km) *</label>
                        <input type="number" id="mileage" name="mileage" value="{{ old('mileage', $car->mileage) }}" required
                               min="0" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                        <input type="text" id="color" name="color" value="{{ old('color', $car->color) }}"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="fuel_type" class="block text-sm font-medium text-gray-700 mb-2">Fuel Type *</label>
                        <select id="fuel_type" name="fuel_type" required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Fuel Type</option>
                            <option value="petrol" {{ old('fuel_type', $car->fuel_type) === 'petrol' ? 'selected' : '' }}>Petrol</option>
                            <option value="diesel" {{ old('fuel_type', $car->fuel_type) === 'diesel' ? 'selected' : '' }}>Diesel</option>
                            <option value="electric" {{ old('fuel_type', $car->fuel_type) === 'electric' ? 'selected' : '' }}>Electric</option>
                            <option value="hybrid" {{ old('fuel_type', $car->fuel_type) === 'hybrid' ? 'selected' : '' }}>Hybrid</option>
                        </select>
                    </div>

                    <div>
                        <label for="transmission" class="block text-sm font-medium text-gray-700 mb-2">Transmission *</label>
                        <select id="transmission" name="transmission" required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Transmission</option>
                            <option value="manual" {{ old('transmission', $car->transmission) === 'manual' ? 'selected' : '' }}>Manual</option>
                            <option value="automatic" {{ old('transmission', $car->transmission) === 'automatic' ? 'selected' : '' }}>Automatic</option>
                            <option value="semi-automatic" {{ old('transmission', $car->transmission) === 'semi-automatic' ? 'selected' : '' }}>Semi-Automatic</option>
                        </select>
                    </div>

                    <div>
                        <label for="body_type" class="block text-sm font-medium text-gray-700 mb-2">Body Type</label>
                        <input type="text" id="body_type" name="body_type" value="{{ old('body_type', $car->body_type) }}"
                               placeholder="e.g., Sedan, Hatchback, SUV"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="engine_size" class="block text-sm font-medium text-gray-700 mb-2">Engine Size</label>
                        <input type="text" id="engine_size" name="engine_size" value="{{ old('engine_size', $car->engine_size) }}"
                               placeholder="e.g., 1.6L, 2.0L"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>

            <!-- Pricing & Location -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Pricing & Location</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="asking_price" class="block text-sm font-medium text-gray-700 mb-2">Asking Price (R) *</label>
                        <input type="number" id="asking_price" name="asking_price" value="{{ old('asking_price', $car->purchase_price) }}" required
                               min="0" step="0.01"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location *</label>
                        <input type="text" id="location" name="location" value="{{ old('location', $car->location) }}" required
                               placeholder="e.g., Johannesburg, Cape Town"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="md:col-span-2">
                        <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                        <input type="text" id="contact_phone" name="contact_phone" value="{{ old('contact_phone', $car->contact_phone) }}"
                               placeholder="Optional - will use your profile phone if not provided"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Description</h2>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Car Description *</label>
                    <textarea id="description" name="description" rows="6" required
                              placeholder="Describe your car's condition, features, service history, etc."
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">{{ old('description', $car->public_description) }}</textarea>
                    <p class="text-sm text-gray-500 mt-1">Maximum 2000 characters</p>
                </div>
            </div>

            <!-- Features -->
            @if($features->count() > 0)
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Features</h2>
                <p class="text-gray-600 mb-4">Select the features your car has:</p>

                @foreach($features as $category => $categoryFeatures)
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-3">{{ ucfirst($category) }}</h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                            @foreach($categoryFeatures as $feature)
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="checkbox" name="features[]" value="{{ $feature->id }}"
                                           {{ in_array($feature->id, old('features', $car->features->pluck('id')->toArray())) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="text-sm text-gray-700">{{ $feature->name }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
            @endif

            <!-- Current Images -->
            @if($car->carImages->count() > 0)
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Current Images</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    @foreach($car->carImages as $image)
                        <div class="relative">
                            <img src="{{ $image->url }}" alt="{{ $image->alt_text }}" class="w-full h-24 object-cover rounded-lg">
                            <div class="absolute top-2 right-2">
                                <input type="checkbox" name="remove_images[]" value="{{ $image->id }}"
                                       class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <label class="text-xs text-red-600 ml-1">Remove</label>
                            </div>
                        </div>
                    @endforeach
                </div>
                <p class="text-sm text-gray-500">Check the boxes above to remove images</p>
            </div>
            @endif

            <!-- Add New Images -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Add New Images</h2>
                <p class="text-gray-600 mb-4">Upload additional images (maximum 10 total). First image will be the main photo.</p>

                <div>
                    <input type="file" id="images" name="images[]" multiple accept="image/*"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-sm text-gray-500 mt-1">Supported formats: JPEG, PNG, JPG, WebP. Maximum 5MB per image.</p>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-between">
                <a href="{{ route('user-cars.show', $car) }}"
                   class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-colors">
                    Cancel
                </a>

                <button type="submit"
                        class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Update Listing
                </button>
            </div>
        </form>
    </div>
</div>
</x-app-layout>

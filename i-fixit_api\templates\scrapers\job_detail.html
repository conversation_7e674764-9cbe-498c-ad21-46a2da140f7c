{% extends "admin/base_site.html" %}

{% block title %}Scraping Job Detail{% endblock %}

{% block content %}
<div class="module">
    <h1>Scraping Job Detail</h1>
    
    <div class="form-row">
        <div>
            <label>ID:</label>
            <div>{{ job.id }}</div>
        </div>
    </div>
    
    <div class="form-row">
        <div>
            <label>Auction Site:</label>
            <div>{{ job.auction_site.name }}</div>
        </div>
    </div>
    
    <div class="form-row">
        <div>
            <label>Status:</label>
            <div>{{ job.get_status_display }}</div>
        </div>
    </div>
    
    <div class="form-row">
        <div>
            <label>Start Time:</label>
            <div>{{ job.start_time|default:"-" }}</div>
        </div>
    </div>
    
    <div class="form-row">
        <div>
            <label>End Time:</label>
            <div>{{ job.end_time|default:"-" }}</div>
        </div>
    </div>
    
    <div class="form-row">
        <div>
            <label>Opportunities Created:</label>
            <div>{{ job.opportunities_created }}</div>
        </div>
    </div>
    
    {% if job.error_message %}
    <div class="form-row">
        <div>
            <label>Error Message:</label>
            <div class="error">{{ job.error_message }}</div>
        </div>
    </div>
    {% endif %}
    
    {% if job.results %}
    <div class="form-row">
        <div>
            <label>Results:</label>
            <pre>{{ job.results|pprint }}</pre>
        </div>
    </div>
    {% endif %}
    
    <div class="submit-row">
        <a href="{% url 'scrapers:job_list' %}" class="button">Back to List</a>
        {% if job.status == 'pending' %}
        <a href="#" class="button run-job" data-job-id="{{ job.id }}">Run Job</a>
        {% endif %}
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const runButton = document.querySelector('.run-job');
        
        if (runButton) {
            runButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                const jobId = this.getAttribute('data-job-id');
                
                fetch(`/scrapers/jobs/${jobId}/run/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('Job started successfully!');
                        window.location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error);
                });
            });
        }
        
        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}

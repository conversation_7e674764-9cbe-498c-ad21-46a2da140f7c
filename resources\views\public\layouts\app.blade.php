<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta Tags -->
    <title>@yield('title', 'AutoLux - Premium Luxury Cars | Exclusive Collection of High-End Vehicles')</title>
    <meta name="description" content="@yield('meta_description', 'Discover the finest luxury cars at AutoLux. Exclusive collection of premium vehicles from prestigious brands. Experience automotive excellence with our curated selection of high-end cars.')">
    <meta name="keywords" content="@yield('meta_keywords', 'luxury cars, premium vehicles, high-end cars, exotic cars, luxury car dealership, prestige automobiles, luxury automotive, premium car sales')">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'AutoLux - Premium Luxury Cars')">
    <meta property="og:description" content="@yield('og_description', 'Discover the finest luxury cars at AutoLux. Exclusive collection of premium vehicles from prestigious brands.')">
    <meta property="og:image" content="@yield('og_image', asset('images/og-default.jpg'))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="AutoLux">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', 'AutoLux - Premium Luxury Cars')">
    <meta name="twitter:description" content="@yield('twitter_description', 'Discover the finest luxury cars at AutoLux. Exclusive collection of premium vehicles.')">
    <meta name="twitter:image" content="@yield('twitter_image', asset('images/og-default.jpg'))">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    @stack('styles')

    <!-- Structured Data -->
    @stack('structured_data')

    <!-- Page-specific head content -->
    @yield('head')

    <!-- Analytics -->
    @if(config('app.env') === 'production')
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
        </script>
    @endif
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Navigation -->
    @include('public.layouts.navigation')

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('public.layouts.footer')

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Search Overlay -->
    <div id="search-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg w-full max-w-2xl p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Search Cars</h3>
                    <button id="close-search" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form action="{{ route('public.cars.index') }}" method="GET" class="space-y-4">
                    <div>
                        <input type="text" name="search" placeholder="Search by make, model, or year..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               autocomplete="off">
                        <div id="search-suggestions" class="mt-2 bg-white border border-gray-200 rounded-lg shadow-lg hidden"></div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <select name="make" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Any Make</option>
                            <!-- Populated via JavaScript -->
                        </select>

                        <select name="price_range" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Any Price</option>
                            <option value="0-200000">Under R200,000</option>
                            <option value="200000-400000">R200,000 - R400,000</option>
                            <option value="400000-600000">R400,000 - R600,000</option>
                            <option value="600000-800000">R600,000 - R800,000</option>
                            <option value="800000-">Over R800,000</option>
                        </select>

                        <select name="year_range" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Any Year</option>
                            <option value="2020-">2020 and newer</option>
                            <option value="2015-2019">2015 - 2019</option>
                            <option value="2010-2014">2010 - 2014</option>
                            <option value="2005-2009">2005 - 2009</option>
                            <option value="-2004">Before 2005</option>
                        </select>
                    </div>

                    <div class="flex justify-between items-center">
                        <a href="{{ route('public.search.index') }}" class="text-blue-600 hover:text-blue-800">
                            Advanced Search
                        </a>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            Search Cars
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script>
        // Global JavaScript variables
        window.appConfig = {
            csrfToken: '{{ csrf_token() }}',
            baseUrl: '{{ url('/') }}',
            routes: {
                searchSuggestions: '{{ route('public.search.suggestions') }}',
                categories: '{{ route('public.categories') }}',
                popularSearches: '{{ route('public.search.popular') }}'
            }
        };
    </script>

    <!-- Additional Scripts -->
    @stack('scripts')

    <!-- Page-specific scripts -->
    @yield('scripts')

    <!-- Public App JavaScript -->
    <script>
        // Basic functionality for public site
        document.addEventListener('DOMContentLoaded', function() {
            // Search overlay functionality
            const searchTriggers = document.querySelectorAll('[data-search-trigger]');
            const searchOverlay = document.getElementById('search-overlay');
            const closeSearch = document.getElementById('close-search');

            searchTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    searchOverlay.classList.remove('hidden');
                    searchOverlay.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                });
            });

            if (closeSearch) {
                closeSearch.addEventListener('click', function() {
                    searchOverlay.classList.add('hidden');
                    searchOverlay.classList.remove('flex');
                    document.body.style.overflow = '';
                });
            }

            // Close search overlay on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !searchOverlay.classList.contains('hidden')) {
                    searchOverlay.classList.add('hidden');
                    searchOverlay.classList.remove('flex');
                    document.body.style.overflow = '';
                }
            });

            // Loading overlay functions
            window.showLoading = function() {
                document.getElementById('loading-overlay').classList.remove('hidden');
                document.getElementById('loading-overlay').classList.add('flex');
            };

            window.hideLoading = function() {
                document.getElementById('loading-overlay').classList.add('hidden');
                document.getElementById('loading-overlay').classList.remove('flex');
            };

            // Toast notification function
            window.showToast = function(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `bg-white border-l-4 p-4 rounded-lg shadow-lg max-w-sm ${
                    type === 'success' ? 'border-green-500' :
                    type === 'error' ? 'border-red-500' :
                    type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
                }`;

                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas ${
                                type === 'success' ? 'fa-check-circle text-green-500' :
                                type === 'error' ? 'fa-exclamation-circle text-red-500' :
                                type === 'warning' ? 'fa-exclamation-triangle text-yellow-500' : 'fa-info-circle text-blue-500'
                            }"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-700">${message}</p>
                        </div>
                        <div class="ml-auto pl-3">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;

                document.getElementById('toast-container').appendChild(toast);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 5000);
            };
        });
    </script>
</body>
</html>

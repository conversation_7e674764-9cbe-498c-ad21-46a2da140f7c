import { SvgIcon, SvgIconProps } from '@mui/material';

const Icon3 = (props: SvgIconProps) => {
  return (
    <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" {...props}>
      <path
        d="M9.33736 7.33687H8.6707V6.67021C8.6707 6.4934 8.60046 6.32383 8.47543 6.1988C8.35041 6.07378 8.18084 6.00354 8.00403 6.00354C7.82722 6.00354 7.65765 6.07378 7.53262 6.1988C7.4076 6.32383 7.33736 6.4934 7.33736 6.67021V7.33687H6.6707C6.49388 7.33687 6.32432 7.40711 6.19929 7.53214C6.07427 7.65716 6.00403 7.82673 6.00403 8.00354C6.00403 8.18035 6.07427 8.34992 6.19929 8.47494C6.32432 8.59997 6.49388 8.67021 6.6707 8.67021H7.33736V9.33687C7.33736 9.51368 7.4076 9.68325 7.53262 9.80828C7.65765 9.9333 7.82722 10.0035 8.00403 10.0035C8.18084 10.0035 8.35041 9.9333 8.47543 9.80828C8.60046 9.68325 8.6707 9.51368 8.6707 9.33687V8.67021H9.33736C9.51417 8.67021 9.68374 8.59997 9.80877 8.47494C9.93379 8.34992 10.004 8.18035 10.004 8.00354C10.004 7.82673 9.93379 7.65716 9.80877 7.53214C9.68374 7.40711 9.51417 7.33687 9.33736 7.33687ZM14.4774 7.53021L12.9107 6.00354V3.76354C12.9107 3.58673 12.8405 3.41716 12.7154 3.29214C12.5904 3.16711 12.4208 3.09687 12.244 3.09687H10.0374L8.47736 1.53021C8.41539 1.46772 8.34165 1.41812 8.26041 1.38428C8.17917 1.35043 8.09204 1.33301 8.00403 1.33301C7.91602 1.33301 7.82888 1.35043 7.74764 1.38428C7.6664 1.41812 7.59267 1.46772 7.53069 1.53021L6.00403 3.09687H3.76403C3.58722 3.09687 3.41765 3.16711 3.29262 3.29214C3.1676 3.41716 3.09736 3.58673 3.09736 3.76354V6.00354L1.53069 7.53021C1.46821 7.59218 1.41861 7.66592 1.38477 7.74716C1.35092 7.8284 1.3335 7.91553 1.3335 8.00354C1.3335 8.09155 1.35092 8.17869 1.38477 8.25992C1.41861 8.34116 1.46821 8.4149 1.53069 8.47687L3.09736 10.0369V12.2435C3.09736 12.4204 3.1676 12.5899 3.29262 12.7149C3.41765 12.84 3.58722 12.9102 3.76403 12.9102H6.00403L7.56403 14.4769C7.626 14.5394 7.69974 14.589 7.78098 14.6228C7.86222 14.6566 7.94935 14.6741 8.03736 14.6741C8.12537 14.6741 8.21251 14.6566 8.29375 14.6228C8.37499 14.589 8.44872 14.5394 8.5107 14.4769L10.0707 12.9102H12.2774C12.4542 12.9102 12.6237 12.84 12.7488 12.7149C12.8738 12.5899 12.944 12.4204 12.944 12.2435V10.0369L14.5107 8.47687C14.571 8.41272 14.6181 8.33725 14.6491 8.25482C14.6801 8.17238 14.6945 8.08461 14.6914 7.99659C14.6883 7.90857 14.6678 7.82203 14.6311 7.74198C14.5943 7.66193 14.5421 7.58995 14.4774 7.53021ZM11.7774 9.29021C11.7144 9.35195 11.6643 9.42558 11.6299 9.50683C11.5956 9.58808 11.5777 9.67534 11.5774 9.76354V11.5769H9.76403C9.67582 11.5772 9.58857 11.5951 9.50732 11.6294C9.42607 11.6638 9.35244 11.7139 9.2907 11.7769L8.00403 13.0635L6.71736 11.7769C6.65562 11.7139 6.58199 11.6638 6.50074 11.6294C6.41949 11.5951 6.33223 11.5772 6.24403 11.5769H4.4307V9.76354C4.43033 9.67534 4.41246 9.58808 4.37813 9.50683C4.3438 9.42558 4.29369 9.35195 4.2307 9.29021L2.94403 8.00354L4.2307 6.71687C4.29369 6.65513 4.3438 6.5815 4.37813 6.50025C4.41246 6.419 4.43033 6.33174 4.4307 6.24354V4.43021H6.24403C6.33223 4.42984 6.41949 4.41197 6.50074 4.37764C6.58199 4.34331 6.65562 4.2932 6.71736 4.23021L8.00403 2.94354L9.2907 4.23021C9.35244 4.2932 9.42607 4.34331 9.50732 4.37764C9.58857 4.41197 9.67582 4.42984 9.76403 4.43021H11.5774V6.24354C11.5777 6.33174 11.5956 6.419 11.6299 6.50025C11.6643 6.5815 11.7144 6.65513 11.7774 6.71687L13.064 8.00354L11.7774 9.29021Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Icon3;

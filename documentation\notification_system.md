# I-fixit Notification System Documentation

## Overview

The I-fixit notification system is a comprehensive, real-time notification platform that keeps users informed about important events, alerts, and updates related to their car investments. The system supports multiple notification channels and provides extensive customization options.

## Features

### ✅ Core Features
- **Real-time Notifications**: WebSocket-based real-time notifications using Laravel Echo and Pusher
- **Multiple Channels**: In-app, email, and SMS (coming soon) notifications
- **Smart Alerts**: Automated notifications based on business rules and thresholds
- **User Preferences**: Granular control over notification types and delivery methods
- **Notification History**: Complete audit trail of all notifications
- **API Support**: RESTful API for mobile and third-party integrations
- **Dashboard Integration**: Notification statistics and quick actions

### 🔔 Notification Types

#### 1. Repair Phase Alerts
- **Trigger**: Cars in repair phase for extended periods
- **Threshold**: Configurable (default: 30 days)
- **Icon**: `fa-wrench`
- **Purpose**: Alert users when repairs are taking longer than expected

#### 2. Dealership Phase Alerts
- **Trigger**: Cars at dealership for extended periods
- **Threshold**: Configurable (default: 60 days)
- **Icon**: `fa-store`
- **Purpose**: Notify when cars aren't selling quickly

#### 3. Budget Exceeded Alerts
- **Trigger**: Repair costs exceed estimated budget
- **Threshold**: Configurable percentage (default: 20%)
- **Icon**: `fa-exclamation-triangle`
- **Purpose**: Warn about cost overruns

#### 4. Investment Opportunity Alerts
- **Trigger**: High-scoring opportunities from AI analysis
- **Icon**: `fa-chart-line`
- **Purpose**: Notify about profitable investment opportunities

#### 5. Public Inquiry Notifications
- **Trigger**: New inquiries from public car listings
- **Icon**: `fa-envelope`
- **Purpose**: Alert about potential buyers

#### 6. Parts Request Notifications
- **Trigger**: Parts requests submitted
- **Icon**: `fa-cogs`
- **Purpose**: Notify about parts ordering activities

#### 7. Sale Completion Notifications
- **Trigger**: Car sales completed
- **Icon**: `fa-handshake` or `fa-chart-line-down`
- **Purpose**: Celebrate successful sales or alert about losses

#### 8. Phase Transition Notifications
- **Trigger**: Car moves between phases
- **Icon**: Phase-specific icons
- **Purpose**: Track car progress through investment cycle

#### 9. Admin Notifications
- **Trigger**: User actions requiring admin attention
- **Icon**: `fa-user-shield`
- **Purpose**: Keep admins informed of system activities

#### 10. Welcome Notifications
- **Trigger**: New user registration
- **Icon**: `fa-hand-wave`
- **Purpose**: Welcome new users and guide them

#### 11. Monthly Report Notifications
- **Trigger**: Monthly performance reports ready
- **Icon**: `fa-chart-bar`
- **Purpose**: Notify about available performance reports

#### 12. System Maintenance Notifications
- **Trigger**: System maintenance or updates
- **Icon**: `fa-tools`
- **Purpose**: Inform users about system status

## Architecture

### Database Schema

#### user_notifications Table
```sql
- id (bigint, primary key)
- user_id (uuid, foreign key to users.id)
- type (string) - notification type identifier
- title (string) - notification title
- message (text) - notification message
- data (text, nullable) - JSON data for additional information
- icon (string, nullable) - FontAwesome icon class
- link (string, nullable) - URL for "View Details" action
- is_read (boolean, default: false)
- read_at (timestamp, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

#### Indexes
- `user_id` - for efficient user queries
- `is_read` - for filtering read/unread notifications

### Key Components

#### 1. NotificationService
**Location**: `app/Services/NotificationService.php`

**Key Methods**:
- `create()` - Create new notifications
- `createForCurrentUser()` - Create for authenticated user
- `createForMultipleUsers()` - Bulk notification creation
- `createForRole()` - Role-based notifications
- `markAsRead()` - Mark notifications as read
- `markAllAsRead()` - Mark all user notifications as read
- `getNotificationStats()` - Get user notification statistics
- `cleanupOldNotifications()` - Remove old read notifications

**Specialized Methods**:
- `createRepairPhaseAlert()`
- `createDealershipPhaseAlert()`
- `createBudgetExceededAlert()`
- `createOpportunityAlert()`
- `createPublicInquiryNotification()`
- `createPartsRequestNotification()`
- `createSaleCompletionNotification()`
- `createPhaseTransitionNotification()`
- `createWelcomeNotification()`
- `createMonthlyReportNotification()`
- `createSystemMaintenanceNotification()`

#### 2. NotificationController
**Location**: `app/Http/Controllers/NotificationController.php`

**Routes**:
- `GET /notifications` - List user notifications
- `POST /notifications/{id}/mark-as-read` - Mark notification as read
- `POST /notifications/mark-all-as-read` - Mark all as read
- `DELETE /notifications/{id}` - Delete notification
- `GET /notifications/unread-count` - Get unread count
- `GET /notifications/recent` - Get recent notifications
- `GET /notifications/stats` - Get notification statistics

#### 3. API Controller
**Location**: `app/Http/Controllers/Api/NotificationController.php`

**API Endpoints**:
- `GET /api/notifications` - List notifications (with pagination)
- `POST /api/notifications/{id}/mark-as-read` - Mark as read
- `POST /api/notifications/mark-all-as-read` - Mark all as read
- `DELETE /api/notifications/{id}` - Delete notification
- `GET /api/notifications/unread-count` - Get unread count
- `GET /api/notifications/recent` - Get recent notifications
- `GET /api/notifications/stats` - Get statistics
- `POST /api/notifications/test` - Send test notification
- `GET /api/notifications/preferences` - Get user preferences

#### 4. Real-time Broadcasting
**Event**: `App\Events\NewNotification`
**Channel**: Private channel per user (`App.Models.User.{user_id}`)
**Technology**: Laravel Echo + Pusher

#### 5. Email Notifications
**Service**: `EmailNotificationService`
**Templates**: Located in `resources/views/emails/notifications/`
- `repair_phase_alert.blade.php`
- `dealership_phase_alert.blade.php`
- `budget_exceeded_alert.blade.php`
- `opportunity_alert.blade.php`

## Automation

### Scheduled Tasks
**Location**: `app/Console/Kernel.php`

```php
// Check car notifications every hour
$schedule->command('app:check-car-notifications')->hourly();

// Run smart notifications every 6 hours
$schedule->command('notifications:smart')->everySixHours();

// Clean up old notifications weekly
$schedule->command('notifications:cleanup')->weekly();
```

### Console Commands

#### 1. CheckCarNotifications
**Command**: `php artisan app:check-car-notifications`
**Purpose**: Check cars for notification triggers
**Frequency**: Hourly

#### 2. RunSmartNotifications
**Command**: `php artisan notifications:smart`
**Purpose**: Run intelligent notification checks
**Frequency**: Every 6 hours

#### 3. CleanupNotifications
**Command**: `php artisan notifications:cleanup`
**Purpose**: Remove old read notifications
**Frequency**: Weekly
**Options**: `--days=90` (default: 90 days)

### Background Jobs

#### SmartNotificationJob
**Location**: `app/Jobs/SmartNotificationJob.php`
**Purpose**: Automated notification checks
**Features**:
- Long fixing phase detection (30+ days)
- High repair cost alerts (>70% of purchase price)
- Extended dealership period alerts (60+ days)
- Upcoming auction notifications (planned)

## User Interface

### Components

#### 1. Notification Dropdown
**Location**: `resources/views/components/notification/dropdown.blade.php`
**Features**:
- Real-time updates via WebSocket
- Recent notifications display
- Unread count badge
- Mark as read functionality
- Mobile-responsive design

#### 2. Notification Settings Card
**Location**: `resources/views/components/notification/settings-card.blade.php`
**Features**:
- Channel preferences (app, email, SMS)
- Notification type toggles
- Threshold configuration
- Quick enable/disable actions
- Test notification button

#### 3. Dashboard Widget
**Location**: `resources/views/components/notification/dashboard-widget.blade.php`
**Features**:
- Notification statistics
- Type breakdown
- Quick actions
- Visual indicators

#### 4. Notification Index Page
**Location**: `resources/views/notifications/index.blade.php`
**Features**:
- Filterable notification list
- Enhanced notification cards
- Bulk actions
- Pagination
- Delete functionality

## Configuration

### User Preferences
**Model**: `UserPreference`
**Table**: `user_preferences`

**Notification Settings**:
- `notification_app` - Enable in-app notifications
- `notification_email` - Enable email notifications
- `notification_sms` - Enable SMS notifications (future)
- `notification_repair_phase` - Repair phase alerts
- `notification_dealership_phase` - Dealership phase alerts
- `notification_budget_exceeded` - Budget exceeded alerts
- `notification_opportunity` - Investment opportunity alerts

**Thresholds**:
- `repair_phase_days_threshold` - Days before repair alert (default: 30)
- `dealership_phase_days_threshold` - Days before dealership alert (default: 60)
- `budget_exceeded_percentage` - Percentage before budget alert (default: 20%)

### Broadcasting Configuration
**File**: `config/broadcasting.php`
**Default Driver**: Pusher
**Channels**: Private channels per user

### Environment Variables
```env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_cluster
```

## Usage Examples

### Creating Notifications

```php
// Basic notification
$notificationService->create(
    $user,
    'custom_alert',
    'Alert Title',
    'Alert message',
    ['custom_data' => 'value'],
    'fa-bell',
    route('dashboard')
);

// Repair phase alert
$notificationService->createRepairPhaseAlert($user, $car, $days);

// For current user
$notificationService->createForCurrentUser(
    'info',
    'Information',
    'This is an info message'
);

// For multiple users
$notificationService->createForMultipleUsers(
    [$userId1, $userId2],
    'announcement',
    'System Announcement',
    'Important system update'
);
```

### API Usage

```javascript
// Get notifications
fetch('/api/notifications', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log(data));

// Mark as read
fetch('/api/notifications/123/mark-as-read', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json'
    }
});
```

## Best Practices

### 1. Performance
- Use pagination for notification lists
- Implement proper indexing
- Clean up old notifications regularly
- Use queued jobs for bulk operations

### 2. User Experience
- Respect user preferences
- Provide clear notification content
- Include relevant action links
- Use appropriate icons and colors

### 3. Security
- Validate user ownership of notifications
- Sanitize notification content
- Use proper authentication for API endpoints
- Implement rate limiting for sensitive actions

### 4. Maintenance
- Monitor notification delivery rates
- Track user engagement with notifications
- Regular cleanup of old data
- Performance monitoring of real-time features

## Troubleshooting

### Common Issues

#### 1. Real-time notifications not working
- Check Pusher configuration
- Verify WebSocket connection
- Ensure Laravel Echo is properly initialized
- Check browser console for errors

#### 2. Email notifications not sending
- Verify SMTP configuration
- Check email queue processing
- Validate email templates
- Review mail logs

#### 3. High notification volume
- Review notification triggers
- Implement rate limiting
- Consider batching similar notifications
- Optimize database queries

#### 4. Performance issues
- Check database indexes
- Monitor query performance
- Implement caching where appropriate
- Consider pagination limits

## Future Enhancements

### Planned Features
1. **SMS Notifications** - Integration with SMS providers
2. **Push Notifications** - Mobile app push notifications
3. **Notification Templates** - Customizable notification templates
4. **Advanced Filtering** - More sophisticated filtering options
5. **Notification Analytics** - Detailed analytics and reporting
6. **Bulk Operations** - Enhanced bulk notification management
7. **Notification Scheduling** - Schedule notifications for future delivery
8. **Rich Content** - Support for rich media in notifications

### Integration Opportunities
- Mobile app notifications
- Slack/Teams integration
- WhatsApp Business API
- Third-party CRM systems
- Business intelligence tools

## Support

For technical support or questions about the notification system:
1. Check this documentation
2. Review the code comments
3. Check the application logs
4. Contact the development team

---

**Last Updated**: January 2025
**Version**: 1.0
**Maintainer**: I-fixit Development Team

{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - I-fixit Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            height: 100vh;
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
        }
        .form-signin {
            max-width: 400px;
            padding: 15px;
        }
        .form-signin .form-floating:focus-within {
            z-index: 2;
        }
        .form-signin input[type="text"] {
            margin-bottom: -1px;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
        .form-signin input[type="password"] {
            margin-bottom: 10px;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
        .logo {
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <main class="form-signin w-100 m-auto">
        <div class="text-center mb-4">
            <div class="logo">
                <i class="fas fa-car-crash text-primary"></i> I-fixit
            </div>
            <h1 class="h3 mb-3 fw-normal">Please sign in</h1>
        </div>

        {% if messages %}
        <div class="messages mb-4">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <form method="post">
            {% csrf_token %}
            <div class="form-floating">
                <input type="text" class="form-control" id="id_username" name="username" placeholder="Username" required>
                <label for="id_username">Username</label>
            </div>
            <div class="form-floating">
                <input type="password" class="form-control" id="id_password" name="password" placeholder="Password" required>
                <label for="id_password">Password</label>
            </div>

            {% if form.errors %}
            <div class="alert alert-danger" role="alert">
                <p>Your username and password didn't match. Please try again.</p>
            </div>
            {% endif %}

            <button class="w-100 btn btn-lg btn-primary" type="submit">Sign in</button>
            <p class="mt-5 mb-3 text-muted text-center">&copy; 2025 I-fixit</p>
        </form>
    </main>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cars', function (Blueprint $table) {
            // User posting fields
            $table->boolean('user_posted')->default(false)->after('public_listing');
            $table->string('contact_phone', 20)->nullable()->after('user_posted');
            $table->string('location', 100)->nullable()->after('contact_phone');
        });

        // Update enum columns using raw SQL to avoid Doctrine issues
        DB::statement("ALTER TABLE cars MODIFY COLUMN current_phase ENUM('bidding', 'fixing', 'dealership', 'sold', 'user_listing') NOT NULL");
        DB::statement("ALTER TABLE cars MODIFY COLUMN status ENUM('active', 'inactive', 'sold', 'pending_review') NOT NULL DEFAULT 'active'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cars', function (Blueprint $table) {
            $table->dropColumn(['user_posted', 'contact_phone', 'location']);
        });

        // Revert enum columns using raw SQL
        DB::statement("ALTER TABLE cars MODIFY COLUMN current_phase ENUM('bidding', 'fixing', 'dealership', 'sold') NOT NULL");
        DB::statement("ALTER TABLE cars MODIFY COLUMN status ENUM('active', 'inactive', 'sold') NOT NULL DEFAULT 'active'");
    }
};

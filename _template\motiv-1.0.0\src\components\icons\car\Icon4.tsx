import { SvgIcon, SvgIconProps } from '@mui/material';

const Icon4 = (props: SvgIconProps) => {
  return (
    <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" {...props}>
      <path
        d="M13.2252 6.38708C13.1699 6.27741 13.0853 6.18518 12.9809 6.12063C12.8764 6.05607 12.7561 6.0217 12.6333 6.02133H9.3083V2.03132C9.31544 1.88547 9.27438 1.74133 9.19145 1.62115C9.10852 1.50096 8.98834 1.41141 8.84945 1.36632C8.71594 1.32239 8.57194 1.3219 8.43812 1.36491C8.30431 1.40792 8.18757 1.49222 8.10465 1.60572L2.78464 8.92073C2.71799 9.01707 2.67796 9.1293 2.66862 9.24608C2.65928 9.36285 2.68095 9.48002 2.73144 9.58573C2.77794 9.70659 2.85872 9.81125 2.96386 9.88686C3.06899 9.96246 3.19392 10.0057 3.3233 10.0113H6.6483V14.0013C6.6484 14.1416 6.69283 14.2782 6.77525 14.3916C6.85766 14.5051 6.97383 14.5896 7.10715 14.6331C7.17396 14.6538 7.24336 14.665 7.3133 14.6663C7.41823 14.6666 7.52173 14.6421 7.61535 14.5947C7.70897 14.5473 7.79004 14.4784 7.85195 14.3937L13.172 7.07868C13.2436 6.97946 13.2865 6.86239 13.2959 6.74036C13.3053 6.61834 13.2808 6.49609 13.2252 6.38708ZM7.9783 11.9531V9.34633C7.9783 9.16996 7.90824 9.00082 7.78353 8.8761C7.65882 8.75139 7.48967 8.68133 7.3133 8.68133H4.6533L7.9783 4.07952V6.68633C7.9783 6.8627 8.04836 7.03184 8.17308 7.15655C8.29779 7.28127 8.46693 7.35133 8.6433 7.35133H11.3033L7.9783 11.9531Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Icon4;

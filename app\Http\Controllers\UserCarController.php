<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\CarFeature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class UserCarController extends Controller
{
    /**
     * Display a listing of user's posted cars.
     */
    public function index()
    {
        $cars = Auth::user()->cars()
            ->where('user_posted', true)
            ->with(['carImages', 'features'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('user-cars.index', compact('cars'));
    }

    /**
     * Show the form for creating a new car listing.
     */
    public function create()
    {
        $features = CarFeature::active()->ordered()->get()->groupBy('category');

        return view('user-cars.create', compact('features'));
    }

    /**
     * Store a newly created car listing.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'make' => 'required|string|max:50',
            'model' => 'required|string|max:50',
            'variant' => 'nullable|string|max:50',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'mileage' => 'required|integer|min:0',
            'color' => 'nullable|string|max:30',
            'fuel_type' => 'required|in:petrol,diesel,electric,hybrid',
            'transmission' => 'required|in:manual,automatic,semi-automatic',
            'body_type' => 'nullable|string|max:30',
            'engine_size' => 'nullable|string|max:20',
            'asking_price' => 'required|numeric|min:0',
            'description' => 'required|string|max:2000',
            'contact_phone' => 'nullable|string|max:20',
            'location' => 'required|string|max:100',
            'features' => 'nullable|array',
            'features.*' => 'exists:car_features,id',
            'images' => 'required|array|min:1|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB max
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Create the car listing
        $car = Car::create([
            'user_id' => Auth::id(),
            'created_by' => Auth::id(),
            'updated_by' => Auth::id(),
            'make' => $request->make,
            'model' => $request->model,
            'variant' => $request->variant,
            'year' => $request->year,
            'mileage' => $request->mileage,
            'color' => $request->color,
            'fuel_type' => $request->fuel_type,
            'transmission' => $request->transmission,
            'body_type' => $request->body_type,
            'engine_size' => $request->engine_size,
            'purchase_price' => $request->asking_price, // Using asking price as purchase price for user listings
            'current_phase' => 'user_listing', // New phase for user-posted cars
            'status' => 'pending_review', // Needs admin approval
            'user_posted' => true,
            'public_description' => $request->description,
            'contact_phone' => $request->contact_phone,
            'location' => $request->location,
            'public_listing' => false, // Will be enabled after admin approval
        ]);

        // Attach features
        if ($request->features) {
            $car->features()->attach($request->features);
        }

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $path = $image->store('car_images/user_posted', 'public');

                $car->carImages()->create([
                    'image_path' => $path,
                    'image_type' => $index === 0 ? 'primary' : 'gallery',
                    'alt_text' => $car->display_name,
                    'sort_order' => $index,
                ]);
            }
        }

        return redirect()->route('user-cars.show', $car)
            ->with('success', 'Your car listing has been submitted for review. We will notify you once it\'s approved.');
    }

    /**
     * Display the specified car listing.
     */
    public function show(Car $car)
    {
        // Ensure user can only view their own cars
        if ($car->user_id !== Auth::id()) {
            abort(403);
        }

        $car->load(['carImages', 'features']);

        return view('user-cars.show', compact('car'));
    }

    /**
     * Show the form for editing the specified car listing.
     */
    public function edit(Car $car)
    {
        // Ensure user can only edit their own cars
        if ($car->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow editing if not yet approved
        if ($car->status === 'active' && $car->public_listing) {
            return redirect()->route('user-cars.show', $car)
                ->with('error', 'This listing has been approved and cannot be edited. Please contact support for changes.');
        }

        $features = CarFeature::active()->ordered()->get()->groupBy('category');
        $selectedFeatures = $car->features->pluck('id')->toArray();

        return view('user-cars.edit', compact('car', 'features', 'selectedFeatures'));
    }

    /**
     * Update the specified car listing.
     */
    public function update(Request $request, Car $car)
    {
        // Ensure user can only update their own cars
        if ($car->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow updating if not yet approved
        if ($car->status === 'active' && $car->public_listing) {
            return redirect()->route('user-cars.show', $car)
                ->with('error', 'This listing has been approved and cannot be edited. Please contact support for changes.');
        }

        $validator = Validator::make($request->all(), [
            'make' => 'required|string|max:50',
            'model' => 'required|string|max:50',
            'variant' => 'nullable|string|max:50',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'mileage' => 'required|integer|min:0',
            'color' => 'nullable|string|max:30',
            'fuel_type' => 'required|in:petrol,diesel,electric,hybrid',
            'transmission' => 'required|in:manual,automatic,semi-automatic',
            'body_type' => 'nullable|string|max:30',
            'engine_size' => 'nullable|string|max:20',
            'asking_price' => 'required|numeric|min:0',
            'description' => 'required|string|max:2000',
            'contact_phone' => 'nullable|string|max:20',
            'location' => 'required|string|max:100',
            'features' => 'nullable|array',
            'features.*' => 'exists:car_features,id',
            'new_images' => 'nullable|array|max:10',
            'new_images.*' => 'image|mimes:jpeg,png,jpg,webp|max:5120',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Update the car
        $car->update([
            'updated_by' => Auth::id(),
            'make' => $request->make,
            'model' => $request->model,
            'variant' => $request->variant,
            'year' => $request->year,
            'mileage' => $request->mileage,
            'color' => $request->color,
            'fuel_type' => $request->fuel_type,
            'transmission' => $request->transmission,
            'body_type' => $request->body_type,
            'engine_size' => $request->engine_size,
            'purchase_price' => $request->asking_price,
            'public_description' => $request->description,
            'contact_phone' => $request->contact_phone,
            'location' => $request->location,
            'status' => 'pending_review', // Reset to pending review after edit
        ]);

        // Update features
        if ($request->features) {
            $car->features()->sync($request->features);
        } else {
            $car->features()->detach();
        }

        // Handle new image uploads
        if ($request->hasFile('new_images')) {
            $currentImageCount = $car->carImages()->count();

            foreach ($request->file('new_images') as $index => $image) {
                if ($currentImageCount + $index < 10) { // Max 10 images
                    $path = $image->store('car_images/user_posted', 'public');

                    $car->carImages()->create([
                        'image_path' => $path,
                        'image_type' => 'gallery',
                        'alt_text' => $car->display_name,
                        'sort_order' => $currentImageCount + $index,
                    ]);
                }
            }
        }

        return redirect()->route('user-cars.show', $car)
            ->with('success', 'Your car listing has been updated and resubmitted for review.');
    }

    /**
     * Remove the specified car listing.
     */
    public function destroy(Car $car)
    {
        // Ensure user can only delete their own cars
        if ($car->user_id !== Auth::id()) {
            abort(403);
        }

        // Delete associated images from storage
        foreach ($car->carImages as $image) {
            Storage::disk('public')->delete($image->image_path);
        }

        $car->delete();

        return redirect()->route('user-cars.index')
            ->with('success', 'Your car listing has been deleted.');
    }

    /**
     * Delete a specific image from a car listing.
     */
    public function deleteImage(Car $car, $imageId)
    {
        // Ensure user can only modify their own cars
        if ($car->user_id !== Auth::id()) {
            abort(403);
        }

        $image = $car->carImages()->findOrFail($imageId);

        // Don't allow deleting if it's the only image
        if ($car->carImages()->count() <= 1) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete the last image. At least one image is required.'
            ], 400);
        }

        // Delete from storage
        Storage::disk('public')->delete($image->image_path);

        // Delete from database
        $image->delete();

        return response()->json([
            'success' => true,
            'message' => 'Image deleted successfully.'
        ]);
    }
}

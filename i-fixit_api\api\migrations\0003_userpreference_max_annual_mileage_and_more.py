# Generated by Django 4.2.21 on 2025-05-13 07:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0002_alter_opportunity_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='userpreference',
            name='max_annual_mileage',
            field=models.IntegerField(blank=True, help_text='Maximum annual mileage in kilometers', null=True),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='max_mileage',
            field=models.IntegerField(blank=True, help_text='Maximum odometer reading in kilometers', null=True),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='min_score_for_notification',
            field=models.IntegerField(default=70, help_text='Minimum opportunity score to trigger notification'),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='preferred_sources',
            field=models.JSONField(blank=True, help_text='List of preferred auction sources', null=True),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='preferred_vehicle_codes',
            field=models.JSONField(blank=True, help_text="List of preferred vehicle codes (e.g., ['1', '2'])", null=True),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='require_battery',
            field=models.BooleanField(default=False, help_text='Only show vehicles that have a battery'),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='require_keys',
            field=models.BooleanField(default=False, help_text='Only show vehicles that have keys'),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='require_spare_wheel',
            field=models.BooleanField(default=False, help_text='Only show vehicles that have a spare wheel'),
        ),
        migrations.AddField(
            model_name='userpreference',
            name='require_starts',
            field=models.BooleanField(default=False, help_text='Only show vehicles that start'),
        ),
    ]

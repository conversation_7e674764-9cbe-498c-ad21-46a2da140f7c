# Two-Factor Authentication Guide

## Overview

Two-Factor Authentication (2FA) adds an extra layer of security to your I-fixit account. When enabled, you'll need both your password and a temporary code generated by an authenticator app on your phone to log in.

## Benefits of Two-Factor Authentication

- **Enhanced Security**: Even if someone discovers your password, they can't access your account without the second factor.
- **Protection Against Phishing**: Authenticator codes change every 30 seconds and are valid only once.
- **Peace of Mind**: Know that your investment data is protected by multiple layers of security.

## Requirements

To use Two-Factor Authentication, you'll need:

1. A smartphone with an authenticator app installed, such as:
   - Google Authenticator ([Android](https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2) | [iOS](https://apps.apple.com/us/app/google-authenticator/id388497605))
   - Microsoft Authenticator ([Android](https://play.google.com/store/apps/details?id=com.azure.authenticator) | [iOS](https://apps.apple.com/us/app/microsoft-authenticator/id983156458))
   - <PERSON><PERSON> ([Android](https://play.google.com/store/apps/details?id=com.authy.authy) | [iOS](https://apps.apple.com/us/app/authy/id494168017))

2. Access to your I-fixit account

## Enabling Two-Factor Authentication

1. Log in to your I-fixit account
2. Navigate to your profile settings by clicking on your name in the top-right corner and selecting "Profile"
3. Scroll down to the "Two Factor Authentication" section
4. Click the "Enable Two-Factor Authentication" button
5. Enter your password to confirm your identity
6. You'll be presented with a QR code
7. Open your authenticator app on your phone and scan the QR code
   - In Google Authenticator, tap the "+" icon and select "Scan a QR code"
   - In Microsoft Authenticator, tap "Add account" and select "Other account (Google, Facebook, etc.)"
   - In Authy, tap the "+" icon and scan the QR code
8. Once scanned, your authenticator app will display a 6-digit code that changes every 30 seconds
9. Enter the current 6-digit code from your authenticator app into the verification field
10. Click "Confirm" to complete the setup

## Recovery Codes

After enabling 2FA, you'll be provided with recovery codes. These are one-time use codes that can be used to access your account if you lose your phone or can't access your authenticator app.

**Important**: Store these recovery codes in a secure place, such as a password manager or printed and stored in a safe location. Each code can only be used once.

## Logging In with Two-Factor Authentication

1. Enter your email and password as usual
2. You'll be prompted for your two-factor authentication code
3. Open your authenticator app and enter the 6-digit code displayed
4. Click "Log in" to complete the authentication process

If you've lost access to your authenticator app, click "Use a recovery code" and enter one of your recovery codes.

## Managing Two-Factor Authentication

### Regenerating Recovery Codes

If you've used some of your recovery codes or want to generate new ones:

1. Go to your profile settings
2. Scroll to the "Two Factor Authentication" section
3. Click "Regenerate Recovery Codes"
4. Store the new codes securely

### Disabling Two-Factor Authentication

If you wish to disable 2FA:

1. Go to your profile settings
2. Scroll to the "Two Factor Authentication" section
3. Click "Disable Two-Factor Authentication"
4. Confirm your decision

**Note**: Disabling 2FA will reduce the security of your account. We recommend keeping it enabled for maximum protection.

## Troubleshooting

### "Invalid authentication code" Error

- Ensure your phone's time is correctly synchronized
- Check that you're entering the most current code (they change every 30 seconds)
- Make sure you're entering the code for the correct account

### Lost Phone or Authenticator App

If you've lost your phone or can't access your authenticator app:

1. Use one of your recovery codes to log in
2. Once logged in, disable 2FA or set it up on a new device

### All Recovery Codes Used

If you've used all your recovery codes and can't access your authenticator app, contact your system administrator for assistance.

## Support

If you encounter any issues with Two-Factor Authentication, please contact <NAME_EMAIL> or call our helpdesk at (*************.

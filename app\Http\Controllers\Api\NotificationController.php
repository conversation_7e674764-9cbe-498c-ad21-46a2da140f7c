<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * The notification service instance.
     *
     * @var \App\Services\NotificationService
     */
    protected $notificationService;

    /**
     * Create a new controller instance.
     *
     * @param \App\Services\NotificationService $notificationService
     * @return void
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth:sanctum');
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of the user's notifications.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $filter = $request->get('filter');
        $perPage = $request->get('per_page', 15);
        
        $query = Auth::user()->notifications();

        // Apply filter if provided
        if ($filter === 'unread') {
            $query->where('is_read', false);
        } elseif ($filter === 'read') {
            $query->where('is_read', true);
        }

        // Get notifications with pagination
        $notifications = $query->orderBy('created_at', 'desc')->paginate($perPage);
        $unreadCount = Auth::user()->unreadNotifications()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'notifications' => $notifications->items(),
                'pagination' => [
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                    'per_page' => $notifications->perPage(),
                    'total' => $notifications->total(),
                    'has_more_pages' => $notifications->hasMorePages(),
                ],
                'unread_count' => $unreadCount,
            ],
        ]);
    }

    /**
     * Mark a notification as read.
     *
     * @param \App\Models\Notification $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Notification $notification)
    {
        // Check if the notification belongs to the current user
        if ($notification->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.',
            ], 403);
        }

        $this->notificationService->markAsRead($notification);

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read.',
            'data' => $notification->fresh(),
        ]);
    }

    /**
     * Mark all notifications as read for the current user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead()
    {
        $count = $this->notificationService->markAllAsRead();

        return response()->json([
            'success' => true,
            'message' => "{$count} notifications marked as read.",
            'data' => [
                'marked_count' => $count,
            ],
        ]);
    }

    /**
     * Get the current user's unread notifications count.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount()
    {
        $count = Auth::user()->unreadNotifications()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'unread_count' => $count,
            ],
        ]);
    }

    /**
     * Get the current user's recent notifications.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecent(Request $request)
    {
        $limit = $request->get('limit', 5);
        
        $notifications = Auth::user()->notifications()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'notifications' => $notifications,
                'unread_count' => Auth::user()->unreadNotifications()->count(),
            ],
        ]);
    }

    /**
     * Get notification statistics for the current user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        $stats = $this->notificationService->getNotificationStats();

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Delete a notification.
     *
     * @param \App\Models\Notification $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Notification $notification)
    {
        // Check if the notification belongs to the current user
        if ($notification->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.',
            ], 403);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully.',
        ]);
    }

    /**
     * Create a test notification for the current user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTestNotification()
    {
        $notification = $this->notificationService->createForCurrentUser(
            'test',
            'API Test Notification',
            'This is a test notification sent via the API to verify the notification system.',
            ['source' => 'api'],
            'fa-bell',
            null
        );

        return response()->json([
            'success' => true,
            'message' => 'Test notification sent successfully!',
            'data' => $notification,
        ]);
    }

    /**
     * Get notification preferences for the current user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPreferences()
    {
        $user = Auth::user();
        $preferences = $user->preferences;

        if (!$preferences) {
            return response()->json([
                'success' => false,
                'message' => 'No preferences found for user.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'notification_app' => $preferences->notification_app,
                'notification_email' => $preferences->notification_email,
                'notification_sms' => $preferences->notification_sms,
                'notification_repair_phase' => $preferences->notification_repair_phase,
                'notification_dealership_phase' => $preferences->notification_dealership_phase,
                'notification_budget_exceeded' => $preferences->notification_budget_exceeded,
                'notification_opportunity' => $preferences->notification_opportunity,
                'repair_phase_days_threshold' => $preferences->repair_phase_days_threshold,
                'dealership_phase_days_threshold' => $preferences->dealership_phase_days_threshold,
                'budget_exceeded_percentage' => $preferences->budget_exceeded_percentage,
            ],
        ]);
    }
}

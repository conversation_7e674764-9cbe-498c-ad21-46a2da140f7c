<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('My Car Listings') }}
        </h2>
    </x-slot>
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">My Car Listings</h1>
            <p class="text-gray-600 mt-2">Manage your posted car listings</p>
        </div>
        <a href="{{ route('user-cars.create') }}"
           class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Post New Car
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {{ session('error') }}
        </div>
    @endif

    @if($cars->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($cars as $car)
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <!-- Car Image -->
                    <div class="relative h-48">
                        @if($car->carImages->count() > 0)
                            <img src="{{ $car->carImages->first()->url }}"
                                 alt="{{ $car->display_name }}"
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-car text-4xl text-gray-400"></i>
                            </div>
                        @endif

                        <!-- Status Badge -->
                        <div class="absolute top-3 right-3">
                            @if($car->status === 'pending_review')
                                <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                    Pending Review
                                </span>
                            @elseif($car->status === 'active' && $car->public_listing)
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                    Live
                                </span>
                            @else
                                <span class="bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                    Draft
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Car Details -->
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $car->display_name }}</h3>

                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <div class="flex justify-between">
                                <span>Year:</span>
                                <span class="font-medium">{{ $car->year }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Mileage:</span>
                                <span class="font-medium">{{ number_format($car->mileage) }} km</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Price:</span>
                                <span class="font-medium text-green-600">R{{ number_format($car->purchase_price) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Location:</span>
                                <span class="font-medium">{{ $car->location }}</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <a href="{{ route('user-cars.show', $car) }}"
                               class="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded hover:bg-blue-700 transition-colors text-sm">
                                View
                            </a>

                            @if($car->status === 'pending_review' || !$car->public_listing)
                                <a href="{{ route('user-cars.edit', $car) }}"
                                   class="flex-1 bg-gray-600 text-white text-center py-2 px-3 rounded hover:bg-gray-700 transition-colors text-sm">
                                    Edit
                                </a>
                            @endif

                            <form action="{{ route('user-cars.destroy', $car) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        onclick="return confirm('Are you sure you want to delete this listing?')"
                                        class="bg-red-600 text-white py-2 px-3 rounded hover:bg-red-700 transition-colors text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $cars->links() }}
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-car text-3xl text-gray-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No car listings yet</h3>
            <p class="text-gray-600 mb-6">Start by posting your first car for sale</p>
            <a href="{{ route('user-cars.create') }}"
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Post Your First Car
            </a>
        </div>
    @endif
</div>
</x-app-layout>

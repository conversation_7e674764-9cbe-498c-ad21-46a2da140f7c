<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\PublicInquiry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;

class PublicInquiryController extends Controller
{
    /**
     * Store a new inquiry
     */
    public function store(Request $request)
    {
        // Rate limiting to prevent spam
        $key = 'inquiry:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => "Too many inquiries. Please try again in {$seconds} seconds."
            ], 429);
        }

        $validator = Validator::make($request->all(), [
            'car_id' => 'required|exists:cars,id',
            'name' => 'required|string|max:100',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|max:1000',
            'inquiry_type' => 'required|in:general,test_drive,financing,trade_in,investment',
            'additional_data' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if car is publicly available
        $car = Car::find($request->car_id);
        if (!$car || !$car->public_listing || !$car->is_available) {
            return response()->json([
                'success' => false,
                'message' => 'This car is no longer available for inquiries.'
            ], 404);
        }

        // Create the inquiry
        $inquiry = PublicInquiry::create([
            'car_id' => $request->car_id,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->message,
            'inquiry_type' => $request->inquiry_type,
            'additional_data' => $request->additional_data,
        ]);

        // Increment car inquiry count
        $car->incrementInquiryCount();

        // Send notification email to admin
        $this->sendInquiryNotification($inquiry);

        // Send confirmation email to customer
        $this->sendCustomerConfirmation($inquiry);

        // Record rate limit attempt
        RateLimiter::hit($key, 300); // 5 minutes

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your inquiry! We will contact you within 24 hours.',
            'inquiry_id' => $inquiry->id,
        ]);
    }

    /**
     * Get inquiry form for a specific car
     */
    public function form(Car $car)
    {
        if (!$car->public_listing || !$car->is_available) {
            abort(404);
        }

        $inquiryTypes = [
            'general' => 'General Inquiry',
            'test_drive' => 'Schedule Test Drive',
            'financing' => 'Financing Options',
            'trade_in' => 'Trade-in Evaluation',
            'investment' => 'Investment Consultation',
        ];

        return view('public.inquiries.form', compact('car', 'inquiryTypes'));
    }

    /**
     * Show inquiry confirmation
     */
    public function confirmation($inquiryId)
    {
        $inquiry = PublicInquiry::with('car')->findOrFail($inquiryId);

        return view('public.inquiries.confirmation', compact('inquiry'));
    }

    /**
     * Get inquiry statistics for a car
     */
    public function getCarInquiryStats(Car $car)
    {
        if (!$car->public_listing) {
            return response()->json(['error' => 'Car not found'], 404);
        }

        $stats = [
            'total_inquiries' => $car->inquiry_count,
            'recent_inquiries' => $car->publicInquiries()
                ->where('created_at', '>=', now()->subDays(7))
                ->count(),
            'inquiry_types' => $car->publicInquiries()
                ->selectRaw('inquiry_type, COUNT(*) as count')
                ->groupBy('inquiry_type')
                ->pluck('count', 'inquiry_type'),
        ];

        return response()->json($stats);
    }

    /**
     * Contact form for general inquiries
     */
    public function contact(Request $request)
    {
        if ($request->isMethod('post')) {
            return $this->storeContactInquiry($request);
        }

        return view('public.contact.index');
    }

    /**
     * Store general contact inquiry
     */
    private function storeContactInquiry(Request $request)
    {
        // Rate limiting
        $key = 'contact:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => "Too many contact attempts. Please try again in {$seconds} seconds.",
                    'errors' => ['rate_limit' => "Too many contact attempts. Please try again in {$seconds} seconds."]
                ], 429);
            }

            return back()->withErrors([
                'message' => "Too many contact attempts. Please try again in {$seconds} seconds."
            ]);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:200',
            'message' => 'required|string|max:1000',
            'inquiry_type' => 'required|in:general,financing,trade_in,investment,other',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your input and try again.',
                    'errors' => $validator->errors()
                ], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        // Create general inquiry (without specific car)
        $inquiry = PublicInquiry::create([
            'car_id' => null, // General inquiry
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->subject . "\n\n" . $request->message,
            'inquiry_type' => $request->inquiry_type,
            'additional_data' => ['subject' => $request->subject],
        ]);

        // Send notifications
        $this->sendInquiryNotification($inquiry);
        $this->sendCustomerConfirmation($inquiry);

        // Record rate limit
        RateLimiter::hit($key, 600); // 10 minutes

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Thank you for contacting us! We will respond within 24 hours.',
                'inquiry_id' => $inquiry->id,
                'redirect_url' => route('public.contact.confirmation', $inquiry->id)
            ]);
        }

        return redirect()->route('public.contact.confirmation', $inquiry->id)
            ->with('success', 'Thank you for contacting us! We will respond within 24 hours.');
    }

    /**
     * Send inquiry notification to admin
     */
    private function sendInquiryNotification(PublicInquiry $inquiry)
    {
        try {
            // TODO: Implement email notification to admin
            // Mail::to(config('app.admin_email'))->send(new InquiryNotification($inquiry));
        } catch (\Exception $e) {
            \Log::error('Failed to send inquiry notification: ' . $e->getMessage());
        }
    }

    /**
     * Send confirmation email to customer
     */
    private function sendCustomerConfirmation(PublicInquiry $inquiry)
    {
        try {
            // TODO: Implement customer confirmation email
            // Mail::to($inquiry->email)->send(new InquiryConfirmation($inquiry));
        } catch (\Exception $e) {
            \Log::error('Failed to send customer confirmation: ' . $e->getMessage());
        }
    }

    /**
     * Get inquiry types for forms
     */
    public function getInquiryTypes()
    {
        return response()->json([
            'general' => 'General Inquiry',
            'test_drive' => 'Schedule Test Drive',
            'financing' => 'Financing Options',
            'trade_in' => 'Trade-in Evaluation',
            'investment' => 'Investment Consultation',
        ]);
    }

    /**
     * Validate phone number format
     */
    private function validatePhoneNumber($phone)
    {
        // South African phone number validation
        $pattern = '/^(\+27|0)[0-9]{9}$/';
        return preg_match($pattern, $phone);
    }

    /**
     * Get inquiry form fields based on type
     */
    public function getFormFields(Request $request)
    {
        $type = $request->get('type', 'general');

        $baseFields = [
            'name' => ['type' => 'text', 'required' => true, 'label' => 'Full Name'],
            'email' => ['type' => 'email', 'required' => true, 'label' => 'Email Address'],
            'phone' => ['type' => 'tel', 'required' => false, 'label' => 'Phone Number'],
            'message' => ['type' => 'textarea', 'required' => true, 'label' => 'Message'],
        ];

        $additionalFields = [];

        switch ($type) {
            case 'test_drive':
                $additionalFields = [
                    'preferred_date' => ['type' => 'date', 'required' => true, 'label' => 'Preferred Date'],
                    'preferred_time' => ['type' => 'time', 'required' => true, 'label' => 'Preferred Time'],
                    'driving_license' => ['type' => 'text', 'required' => true, 'label' => 'Driving License Number'],
                ];
                break;
            case 'financing':
                $additionalFields = [
                    'monthly_income' => ['type' => 'number', 'required' => false, 'label' => 'Monthly Income'],
                    'deposit_amount' => ['type' => 'number', 'required' => false, 'label' => 'Deposit Amount'],
                    'employment_status' => ['type' => 'select', 'required' => false, 'label' => 'Employment Status', 'options' => [
                        'employed' => 'Employed',
                        'self_employed' => 'Self Employed',
                        'retired' => 'Retired',
                        'student' => 'Student',
                    ]],
                ];
                break;
            case 'trade_in':
                $additionalFields = [
                    'trade_make' => ['type' => 'text', 'required' => false, 'label' => 'Trade-in Make'],
                    'trade_model' => ['type' => 'text', 'required' => false, 'label' => 'Trade-in Model'],
                    'trade_year' => ['type' => 'number', 'required' => false, 'label' => 'Trade-in Year'],
                    'trade_mileage' => ['type' => 'number', 'required' => false, 'label' => 'Trade-in Mileage'],
                ];
                break;
        }

        return response()->json([
            'fields' => array_merge($baseFields, $additionalFields),
            'type' => $type,
        ]);
    }
}

{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}Dashboard - I-fixit{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
    </div>

    <!-- Summary Cards Row -->
    <div class="row">
        <!-- Total Opportunities Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 summary-card summary-card-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Opportunities</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_opportunities }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-search-dollar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Opportunities Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 summary-card summary-card-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                New Opportunities</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ new_opportunities }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interested Opportunities Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 summary-card summary-card-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Interested</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ interested_opportunities }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bidding Opportunities Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2 summary-card summary-card-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Bidding</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ bidding_opportunities }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-gavel fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Opportunities -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Opportunities</h6>
                    <a href="{% url 'dashboard:opportunities' %}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_opportunities %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Vehicle</th>
                                    <th>Source</th>
                                    <th>Score</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for opportunity in recent_opportunities %}
                                <tr>
                                    <td>
                                        <a href="{% url 'dashboard:opportunity_detail' opportunity.id %}">
                                            {{ opportunity.year }} {{ opportunity.make }} {{ opportunity.model }}
                                        </a>
                                    </td>
                                    <td>{{ opportunity.source }}</td>
                                    <td>{{ opportunity.opportunity_score }}</td>
                                    <td>
                                        <span class="badge badge-{{ opportunity.status }}">
                                            {{ opportunity.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-center">No opportunities found.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Scraping Jobs -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Scraping Jobs</h6>
                    <a href="{% url 'dashboard:scrapers' %}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_jobs %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Auction Site</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Results</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in recent_jobs %}
                                <tr>
                                    <td>{{ job.auction_site.name }}</td>
                                    <td>
                                        {% if job.status == 'pending' %}
                                        <span class="badge bg-secondary">Pending</span>
                                        {% elif job.status == 'running' %}
                                        <span class="badge bg-primary">Running</span>
                                        {% elif job.status == 'completed' %}
                                        <span class="badge bg-success">Completed</span>
                                        {% elif job.status == 'failed' %}
                                        <span class="badge bg-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ job.created_at|date:"M d, Y H:i" }}</td>
                                    <td>{{ job.opportunities_created }} opportunities</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-center">No scraping jobs found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

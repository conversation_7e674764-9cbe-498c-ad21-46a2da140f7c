<?php

namespace Database\Seeders;

use App\Models\CarFeature;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CarFeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $features = [
            // Safety Features
            ['name' => 'ABS Brakes', 'category' => 'Safety', 'icon' => 'fa-shield-alt', 'sort_order' => 1],
            ['name' => 'Airbags (Front)', 'category' => 'Safety', 'icon' => 'fa-shield-alt', 'sort_order' => 2],
            ['name' => 'Airbags (Side)', 'category' => 'Safety', 'icon' => 'fa-shield-alt', 'sort_order' => 3],
            ['name' => 'Electronic Stability Control', 'category' => 'Safety', 'icon' => 'fa-shield-alt', 'sort_order' => 4],
            ['name' => 'Traction Control', 'category' => 'Safety', 'icon' => 'fa-shield-alt', 'sort_order' => 5],
            ['name' => 'Parking Sensors', 'category' => 'Safety', 'icon' => 'fa-parking', 'sort_order' => 6],
            ['name' => 'Reverse Camera', 'category' => 'Safety', 'icon' => 'fa-video', 'sort_order' => 7],
            ['name' => 'Blind Spot Monitoring', 'category' => 'Safety', 'icon' => 'fa-eye', 'sort_order' => 8],
            ['name' => 'Lane Departure Warning', 'category' => 'Safety', 'icon' => 'fa-road', 'sort_order' => 9],
            ['name' => 'Collision Warning', 'category' => 'Safety', 'icon' => 'fa-exclamation-triangle', 'sort_order' => 10],

            // Comfort Features
            ['name' => 'Air Conditioning', 'category' => 'Comfort', 'icon' => 'fa-snowflake', 'sort_order' => 11],
            ['name' => 'Climate Control', 'category' => 'Comfort', 'icon' => 'fa-thermometer-half', 'sort_order' => 12],
            ['name' => 'Heated Seats', 'category' => 'Comfort', 'icon' => 'fa-fire', 'sort_order' => 13],
            ['name' => 'Ventilated Seats', 'category' => 'Comfort', 'icon' => 'fa-wind', 'sort_order' => 14],
            ['name' => 'Power Seats', 'category' => 'Comfort', 'icon' => 'fa-chair', 'sort_order' => 15],
            ['name' => 'Memory Seats', 'category' => 'Comfort', 'icon' => 'fa-save', 'sort_order' => 16],
            ['name' => 'Leather Seats', 'category' => 'Comfort', 'icon' => 'fa-chair', 'sort_order' => 17],
            ['name' => 'Sunroof', 'category' => 'Comfort', 'icon' => 'fa-sun', 'sort_order' => 18],
            ['name' => 'Panoramic Roof', 'category' => 'Comfort', 'icon' => 'fa-sun', 'sort_order' => 19],
            ['name' => 'Cruise Control', 'category' => 'Comfort', 'icon' => 'fa-tachometer-alt', 'sort_order' => 20],

            // Technology Features
            ['name' => 'Bluetooth Connectivity', 'category' => 'Technology', 'icon' => 'fa-bluetooth', 'sort_order' => 21],
            ['name' => 'USB Ports', 'category' => 'Technology', 'icon' => 'fa-usb', 'sort_order' => 22],
            ['name' => 'Wireless Charging', 'category' => 'Technology', 'icon' => 'fa-charging-station', 'sort_order' => 23],
            ['name' => 'Navigation System', 'category' => 'Technology', 'icon' => 'fa-map', 'sort_order' => 24],
            ['name' => 'Apple CarPlay', 'category' => 'Technology', 'icon' => 'fa-apple', 'sort_order' => 25],
            ['name' => 'Android Auto', 'category' => 'Technology', 'icon' => 'fa-android', 'sort_order' => 26],
            ['name' => 'Premium Sound System', 'category' => 'Technology', 'icon' => 'fa-music', 'sort_order' => 27],
            ['name' => 'Digital Dashboard', 'category' => 'Technology', 'icon' => 'fa-digital-tachograph', 'sort_order' => 28],
            ['name' => 'Head-Up Display', 'category' => 'Technology', 'icon' => 'fa-eye', 'sort_order' => 29],
            ['name' => 'Keyless Entry', 'category' => 'Technology', 'icon' => 'fa-key', 'sort_order' => 30],

            // Performance Features
            ['name' => 'Turbo Engine', 'category' => 'Performance', 'icon' => 'fa-tachometer-alt', 'sort_order' => 31],
            ['name' => 'Sport Mode', 'category' => 'Performance', 'icon' => 'fa-flag-checkered', 'sort_order' => 32],
            ['name' => 'Paddle Shifters', 'category' => 'Performance', 'icon' => 'fa-cogs', 'sort_order' => 33],
            ['name' => 'All-Wheel Drive', 'category' => 'Performance', 'icon' => 'fa-road', 'sort_order' => 34],
            ['name' => 'Limited Slip Differential', 'category' => 'Performance', 'icon' => 'fa-cogs', 'sort_order' => 35],
            ['name' => 'Sport Suspension', 'category' => 'Performance', 'icon' => 'fa-car', 'sort_order' => 36],
            ['name' => 'Performance Brakes', 'category' => 'Performance', 'icon' => 'fa-stop-circle', 'sort_order' => 37],
            ['name' => 'Launch Control', 'category' => 'Performance', 'icon' => 'fa-rocket', 'sort_order' => 38],

            // Exterior Features
            ['name' => 'LED Headlights', 'category' => 'Exterior', 'icon' => 'fa-lightbulb', 'sort_order' => 39],
            ['name' => 'Xenon Headlights', 'category' => 'Exterior', 'icon' => 'fa-lightbulb', 'sort_order' => 40],
            ['name' => 'Fog Lights', 'category' => 'Exterior', 'icon' => 'fa-lightbulb', 'sort_order' => 41],
            ['name' => 'Alloy Wheels', 'category' => 'Exterior', 'icon' => 'fa-circle', 'sort_order' => 42],
            ['name' => 'Roof Rails', 'category' => 'Exterior', 'icon' => 'fa-car', 'sort_order' => 43],
            ['name' => 'Tow Bar', 'category' => 'Exterior', 'icon' => 'fa-link', 'sort_order' => 44],
            ['name' => 'Side Steps', 'category' => 'Exterior', 'icon' => 'fa-stairs', 'sort_order' => 45],
            ['name' => 'Spare Wheel', 'category' => 'Exterior', 'icon' => 'fa-circle', 'sort_order' => 46],

            // Interior Features
            ['name' => 'Multi-Function Steering Wheel', 'category' => 'Interior', 'icon' => 'fa-steering-wheel', 'sort_order' => 47],
            ['name' => 'Electric Windows', 'category' => 'Interior', 'icon' => 'fa-window-maximize', 'sort_order' => 48],
            ['name' => 'Central Locking', 'category' => 'Interior', 'icon' => 'fa-lock', 'sort_order' => 49],
            ['name' => 'Cup Holders', 'category' => 'Interior', 'icon' => 'fa-coffee', 'sort_order' => 50],
            ['name' => 'Storage Compartments', 'category' => 'Interior', 'icon' => 'fa-box', 'sort_order' => 51],
            ['name' => 'Rear Armrest', 'category' => 'Interior', 'icon' => 'fa-chair', 'sort_order' => 52],
            ['name' => 'Split Rear Seats', 'category' => 'Interior', 'icon' => 'fa-chair', 'sort_order' => 53],
            ['name' => 'Ambient Lighting', 'category' => 'Interior', 'icon' => 'fa-lightbulb', 'sort_order' => 54],
        ];

        foreach ($features as $feature) {
            CarFeature::updateOrCreate(
                ['name' => $feature['name']],
                $feature
            );
        }
    }
}

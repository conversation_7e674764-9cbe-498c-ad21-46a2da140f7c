@extends('public.layouts.app')

@section('title', $car->display_name . ' | Premium Luxury Vehicle at AutoLux')
@section('meta_description', 'Discover the ' . $car->display_name . ' at AutoLux. Premium luxury vehicle with exceptional quality, verified history, and white-glove service. Experience automotive excellence.')

@section('head')
<meta property="og:title" content="{{ $metaTitle }}">
<meta property="og:description" content="{{ $metaDescription }}">
<meta property="og:image" content="{{ $car->primaryImage?->url ?? asset('images/car-placeholder.jpg') }}">
<meta property="og:url" content="{{ url()->current() }}">
<meta name="twitter:card" content="summary_large_image">



<!-- Schema.org structured data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Car",
  "name": "{{ $car->display_name }}",
  "brand": "{{ $car->make }}",
  "model": "{{ $car->model }}",
  "vehicleModelDate": "{{ $car->year }}",
  "mileageFromOdometer": "{{ $car->mileage }}",
  "fuelType": "{{ $car->fuel_type }}",
  "vehicleTransmission": "{{ $car->transmission }}",
  "offers": {
    "@type": "Offer",
    "price": "{{ $car->purchase_price }}",
    "priceCurrency": "ZAR"
  },
  "image": "{{ $car->primaryImage?->url ?? asset('images/car-placeholder.jpg') }}"
}
</script>
@endsection

@section('content')
<!-- Breadcrumb Navigation -->
<nav class="bg-gray-50 py-4" aria-label="Breadcrumb">
    <div class="container mx-auto px-4">
        <ol class="flex items-center space-x-2 text-sm">
            <li><a href="{{ route('public.home') }}" class="text-blue-600 hover:text-blue-800">Home</a></li>
            <li><span class="text-gray-500">/</span></li>
            <li><a href="{{ route('public.cars.index') }}" class="text-blue-600 hover:text-blue-800">Cars</a></li>
            <li><span class="text-gray-500">/</span></li>
            <li class="text-gray-900 font-medium">{{ $car->display_name }}</li>
        </ol>
    </div>
</nav>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

        <!-- Left Column - Car Images & 3D Viewer -->
        <div class="lg:col-span-2">
            <!-- Car Header -->
            <div class="mb-6" data-scroll-animation="fade-in">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                    <div>
                        <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">{{ $car->display_name }}</h1>
                        <div class="flex items-center space-x-4 text-gray-600">
                            <span><i class="fas fa-eye mr-1"></i> {{ number_format($car->view_count ?? 0) }} views</span>
                            @if($car->featured)
                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                                <i class="fas fa-star mr-1"></i> Featured
                            </span>
                            @endif
                        </div>
                    </div>
                    <div class="mt-4 sm:mt-0 flex space-x-2">
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                                data-click-effect title="Save to favorites">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                                data-click-effect title="Share">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Car Gallery & 3D Viewer -->
            <div class="mb-8" data-scroll-animation="slide-up">
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <!-- Main Image/3D Viewer -->
                    <div class="relative h-96 lg:h-[500px] bg-gradient-to-br from-gray-100 to-gray-200">
                        @if($car->carImages->count() > 0)
                            <div id="car-main-viewer" class="w-full h-full">
                                <!-- Primary Image -->
                                <img id="main-car-image"
                                     src="{{ $car->primaryImage?->url ?? $car->carImages->first()->url }}"
                                     alt="{{ $car->primaryImage?->alt_text ?? $car->display_name }}"
                                     class="w-full h-full object-cover transition-opacity duration-300">

                                <!-- 3D Viewer Placeholder -->
                                <div id="car-3d-viewer" class="absolute inset-0 hidden">
                                    <div class="fallback-car-viewer">
                                        <div class="car-placeholder text-center">
                                            <i class="fas fa-car"></i>
                                            <p>{{ $car->display_name }}</p>
                                            <small>3D View Coming Soon</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- View Toggle -->
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-2">
                                <button id="toggle-3d" class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-cube"></i>
                                    <span>3D View</span>
                                </button>
                            </div>
                        @else
                            <div class="fallback-car-viewer">
                                <div class="car-placeholder text-center">
                                    <i class="fas fa-car"></i>
                                    <p>{{ $car->display_name }}</p>
                                    <small>Images coming soon</small>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Image Thumbnails -->
                    @if($car->carImages->count() > 0)
                    <div class="p-4 bg-gray-50">
                        <div class="flex space-x-3 overflow-x-auto pb-2">
                            @foreach($car->carImages as $index => $image)
                            <button class="thumbnail-btn flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 {{ $index === 0 ? 'border-blue-500' : 'border-gray-200 hover:border-gray-300' }}"
                                    data-image-url="{{ $image->url }}"
                                    data-image-alt="{{ $image->alt_text }}"
                                    data-index="{{ $index }}">
                                <img src="{{ $image->thumbnail_url }}"
                                     alt="{{ $image->alt_text }}"
                                     class="w-full h-full object-cover">
                            </button>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Car Specifications -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-8" data-scroll-animation="slide-up" data-animation-delay="200">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Specifications</h2>

                <div class="grid grid-cols-2 md:grid-cols-3 gap-6">
                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                        <i class="fas fa-calendar text-2xl text-blue-600 mb-2"></i>
                        <div class="text-sm text-gray-600">Year</div>
                        <div class="text-lg font-semibold">{{ $car->year }}</div>
                    </div>

                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                        <i class="fas fa-tachometer-alt text-2xl text-blue-600 mb-2"></i>
                        <div class="text-sm text-gray-600">Mileage</div>
                        <div class="text-lg font-semibold">{{ number_format($car->mileage) }} km</div>
                    </div>

                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                        <i class="fas fa-gas-pump text-2xl text-blue-600 mb-2"></i>
                        <div class="text-sm text-gray-600">Fuel Type</div>
                        <div class="text-lg font-semibold">{{ ucfirst($car->fuel_type) }}</div>
                    </div>

                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                        <i class="fas fa-cogs text-2xl text-blue-600 mb-2"></i>
                        <div class="text-sm text-gray-600">Transmission</div>
                        <div class="text-lg font-semibold">{{ ucfirst($car->transmission) }}</div>
                    </div>

                    @if($car->color)
                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                        <i class="fas fa-palette text-2xl text-blue-600 mb-2"></i>
                        <div class="text-sm text-gray-600">Color</div>
                        <div class="text-lg font-semibold">{{ ucfirst($car->color) }}</div>
                    </div>
                    @endif

                    @if($car->body_type)
                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                        <i class="fas fa-car text-2xl text-blue-600 mb-2"></i>
                        <div class="text-sm text-gray-600">Body Type</div>
                        <div class="text-lg font-semibold">{{ ucfirst($car->body_type) }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Car Features -->
            @if($car->features && $car->features->count() > 0)
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-8" data-scroll-animation="slide-up" data-animation-delay="300">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Features & Equipment</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($car->features->groupBy('category') as $category => $categoryFeatures)
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3 capitalize">{{ $category }}</h3>
                        <ul class="space-y-2">
                            @foreach($categoryFeatures as $feature)
                            <li class="flex items-center text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                {{ $feature->name }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Car Description -->
            @if($car->public_description)
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-8" data-scroll-animation="slide-up" data-animation-delay="400">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Vehicle</h2>
                <div class="prose prose-gray max-w-none">
                    <p class="text-gray-700 leading-relaxed">{{ $car->public_description }}</p>
                </div>
            </div>
            @endif
        </div>

        <!-- Right Column - Price & Actions -->
        <div class="lg:col-span-1">
            <!-- Price & Purchase Card -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 sticky top-4" data-scroll-animation="slide-left">
                <div class="text-center mb-6">
                    <div class="text-4xl font-bold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent mb-2">R{{ number_format($car->purchase_price) }}</div>
                    <div class="text-gray-700 font-semibold text-lg">
                        Premium Luxury Vehicle
                    </div>
                </div>

                <!-- Vehicle Summary -->
                <div class="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-xl p-6 mb-6 border border-amber-100">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-crown text-amber-600 mr-2"></i>
                        Vehicle Details
                    </h3>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Year & Model</span>
                            <span class="font-semibold">{{ $car->year }} {{ $car->make }} {{ $car->model }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Mileage</span>
                            <span class="font-semibold">{{ number_format($car->mileage) }} km</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Fuel Type</span>
                            <span class="font-semibold">{{ ucfirst($car->fuel_type) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Transmission</span>
                            <span class="font-semibold">{{ ucfirst($car->transmission) }}</span>
                        </div>

                        @if($car->color)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Color</span>
                            <span class="font-semibold">{{ ucfirst($car->color) }}</span>
                        </div>
                        @endif

                        <div class="pt-3 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Condition</span>
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ $car->mileage < 50000 ? '90' : ($car->mileage < 100000 ? '75' : '60') }}%"></div>
                                    </div>
                                    <span class="text-sm font-semibold">{{ $car->mileage < 50000 ? 'Excellent' : ($car->mileage < 100000 ? 'Good' : 'Fair') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    <button class="w-full bg-gradient-to-r from-amber-500 to-yellow-600 text-black py-4 px-4 rounded-xl font-semibold hover:from-amber-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                            data-click-effect
                            onclick="openInquiryModal()">
                        <i class="fas fa-concierge-bell mr-2"></i>
                        Concierge Service
                    </button>

                    <button class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-4 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                            data-click-effect
                            onclick="openCalculatorModal()">
                        <i class="fas fa-calculator mr-2"></i>
                        Premium Financing
                    </button>

                    <button class="w-full bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 py-4 px-4 rounded-xl font-semibold hover:from-gray-200 hover:to-gray-300 transition-all duration-300 transform hover:scale-105 shadow-md border border-gray-300"
                            data-click-effect>
                        <i class="fas fa-car mr-2"></i>
                        Private Viewing
                    </button>
                </div>

                <!-- Contact Info -->
                <div class="mt-6 pt-6 border-t border-gray-200 text-center">
                    <p class="text-sm text-gray-600 mb-2">Need help? Call us directly</p>
                    <a href="tel:+27123456789" class="text-blue-600 font-semibold hover:text-blue-800">
                        <i class="fas fa-phone mr-1"></i>
                        +27 12 345 6789
                    </a>
                </div>
            </div>

            <!-- Similar Cars -->
            @if($similarCars->count() > 0)
            <div class="bg-white rounded-2xl shadow-lg p-6" data-scroll-animation="slide-left" data-animation-delay="200">
                <h3 class="text-xl font-bold text-gray-900 mb-4">Similar Cars</h3>

                <div class="space-y-4">
                    @foreach($similarCars as $similarCar)
                    @if($similarCar->slug)
                    <a href="{{ route('public.cars.show', $similarCar->slug) }}"
                       class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all">
                    @else
                    <div class="block p-4 border border-gray-200 rounded-lg opacity-50">
                    @endif
                        <div class="flex items-center space-x-3">
                            @if($similarCar->primaryImage)
                            <img src="{{ $similarCar->primaryImage->thumbnail_url }}"
                                 alt="{{ $similarCar->display_name }}"
                                 class="w-16 h-12 object-cover rounded">
                            @else
                            <div class="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
                                <i class="fas fa-car text-gray-400"></i>
                            </div>
                            @endif

                            <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-gray-900 truncate">{{ $similarCar->display_name }}</h4>
                                <p class="text-sm text-gray-600">{{ $similarCar->year }} • {{ number_format($similarCar->mileage) }}km</p>
                                <p class="text-blue-600 font-semibold">R{{ number_format($similarCar->purchase_price) }}</p>
                            </div>
                        </div>
                    @if($similarCar->slug)
                    </a>
                    @else
                    </div>
                    @endif
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Inquiry Modal -->
<div id="inquiryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900">Request Information</h3>
                <button onclick="closeInquiryModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="inquiryForm" class="space-y-4">
                @csrf
                <input type="hidden" name="car_id" value="{{ $car->id }}">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Inquiry Type</label>
                    <select name="inquiry_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select inquiry type...</option>
                        <option value="general">General Inquiry</option>
                        <option value="private_viewing">Private Viewing</option>
                        <option value="financing">Premium Financing</option>
                        <option value="concierge">Concierge Service</option>
                        <option value="trade_in">Trade-in Evaluation</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <input type="text" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" name="email" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <input type="tel" name="phone"
                           placeholder="+27 12 345 6789"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                    <textarea name="message" rows="4" required
                              placeholder="I'm interested in the {{ $car->display_name }}. Please arrange a private viewing and provide information about your concierge services and premium financing options."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"></textarea>
                </div>

                <button type="submit" id="inquirySubmitBtn"
                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="submit-text">Send Inquiry</span>
                    <span class="loading-text hidden">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Sending...
                    </span>
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Finance Calculator Modal -->
<div id="calculatorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900">Finance Calculator</h3>
                <button onclick="closeCalculatorModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Vehicle Price</label>
                    <input type="number" id="calc-vehicle-price" value="{{ $car->purchase_price }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Down Payment</label>
                    <input type="number" id="calc-down-payment" value="{{ $car->purchase_price * 0.1 }}" min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Loan Term (months)</label>
                    <select id="calc-loan-term" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="36">36 months</option>
                        <option value="48">48 months</option>
                        <option value="60" selected>60 months</option>
                        <option value="72">72 months</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Interest Rate (%)</label>
                    <input type="number" id="calc-interest-rate" value="10.5" min="0" max="30" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-2">Monthly Payment Breakdown</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Loan Amount:</span>
                            <span id="calc-loan-amount" class="font-semibold">R0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Monthly Payment:</span>
                            <span id="calc-monthly-payment" class="font-semibold text-blue-600">R0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Total Interest:</span>
                            <span id="calc-total-interest" class="font-semibold text-orange-600">R0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Total Cost:</span>
                            <span id="calc-total-cost" class="font-semibold text-gray-900">R0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// Image Gallery Functionality
document.addEventListener('DOMContentLoaded', function() {
    const thumbnails = document.querySelectorAll('.thumbnail-btn');
    const mainImage = document.getElementById('main-car-image');

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const imageUrl = this.dataset.imageUrl;
            const imageAlt = this.dataset.imageAlt;

            // Update main image
            mainImage.src = imageUrl;
            mainImage.alt = imageAlt;

            // Update active thumbnail
            thumbnails.forEach(t => t.classList.remove('border-blue-500'));
            thumbnails.forEach(t => t.classList.add('border-gray-200'));
            this.classList.remove('border-gray-200');
            this.classList.add('border-blue-500');
        });
    });

    // 3D Viewer Toggle
    const toggle3D = document.getElementById('toggle-3d');
    const mainViewer = document.getElementById('car-main-viewer');
    const viewer3D = document.getElementById('car-3d-viewer');

    if (toggle3D) {
        toggle3D.addEventListener('click', function() {
            const is3D = viewer3D.classList.contains('hidden');

            if (is3D) {
                mainImage.classList.add('hidden');
                viewer3D.classList.remove('hidden');
                this.innerHTML = '<i class="fas fa-image"></i><span>Photo View</span>';
            } else {
                mainImage.classList.remove('hidden');
                viewer3D.classList.add('hidden');
                this.innerHTML = '<i class="fas fa-cube"></i><span>3D View</span>';
            }
        });
    }

    // Finance Calculator
    const calcInputs = ['calc-vehicle-price', 'calc-down-payment', 'calc-loan-term', 'calc-interest-rate'];
    calcInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', updateCalculator);
            input.addEventListener('change', updateCalculator);
        }
    });

    updateCalculator();
});

function openInquiryModal() {
    document.getElementById('inquiryModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeInquiryModal() {
    document.getElementById('inquiryModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function openCalculatorModal() {
    document.getElementById('calculatorModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
    updateCalculator();
}

function closeCalculatorModal() {
    document.getElementById('calculatorModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function updateCalculator() {
    const vehiclePrice = parseFloat(document.getElementById('calc-vehicle-price')?.value || 0);
    const downPayment = parseFloat(document.getElementById('calc-down-payment')?.value || 0);
    const loanTerm = parseFloat(document.getElementById('calc-loan-term')?.value || 60);
    const interestRate = parseFloat(document.getElementById('calc-interest-rate')?.value || 10.5);

    const loanAmount = vehiclePrice - downPayment;
    const monthlyRate = interestRate / 100 / 12;

    let monthlyPayment = 0;
    if (loanAmount > 0 && monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, loanTerm)) / (Math.pow(1 + monthlyRate, loanTerm) - 1);
    }

    const totalPayments = monthlyPayment * loanTerm;
    const totalInterest = totalPayments - loanAmount;
    const totalCost = vehiclePrice + totalInterest;

    document.getElementById('calc-loan-amount').textContent = 'R' + loanAmount.toLocaleString();
    document.getElementById('calc-monthly-payment').textContent = 'R' + monthlyPayment.toLocaleString();
    document.getElementById('calc-total-interest').textContent = 'R' + totalInterest.toLocaleString();
    document.getElementById('calc-total-cost').textContent = 'R' + totalCost.toLocaleString();
}

// Inquiry Form Submission
document.getElementById('inquiryForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('inquirySubmitBtn');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');

    // Show loading state
    submitBtn.disabled = true;
    submitText.classList.add('hidden');
    loadingText.classList.remove('hidden');

    try {
        const formData = new FormData(this);

        const response = await fetch('{{ route("public.inquiries.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        });

        const data = await response.json();

        if (data.success) {
            // Show success message
            showNotification('success', data.message);
            closeInquiryModal();
            this.reset();
        } else {
            // Show error message
            if (data.errors) {
                const errorMessages = Object.values(data.errors).flat().join('\n');
                showNotification('error', errorMessages);
            } else {
                showNotification('error', data.message || 'An error occurred. Please try again.');
            }
        }
    } catch (error) {
        console.error('Error submitting inquiry:', error);
        showNotification('error', 'Network error. Please check your connection and try again.');
    } finally {
        // Reset button state
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    }
});

// Notification system
function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.id === 'inquiryModal') {
        closeInquiryModal();
    }
    if (e.target.id === 'calculatorModal') {
        closeCalculatorModal();
    }
});
</script>
@endsection

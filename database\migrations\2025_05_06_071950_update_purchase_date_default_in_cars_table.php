<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using SQLite and skip this migration
        // SQLite doesn't support MODIFY syntax and this default is not critical for testing
        if (DB::getDriverName() === 'mysql') {
            // Instead of using change() with DB::raw, which causes issues,
            // we'll use a different approach to set the default value
            DB::statement('ALTER TABLE cars MODIFY purchase_date DATE DEFAULT CURRENT_DATE');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if we're using SQLite and skip this migration
        if (DB::getDriverName() === 'mysql') {
            // Use direct SQL to remove the default value
            DB::statement('ALTER TABLE cars MODIFY purchase_date DATE DEFAULT NULL');
        }
    }
};

import { SvgIcon, SvgIconProps } from '@mui/material';

const Lightning = (props: SvgIconProps) => {
  return (
    <SvgIcon width="20" height="21" viewBox="0 0 20 21" fill="none" {...props}>
      <g clipPath="url(#clip0_683_608)">
        <path
          d="M3.45417 11.6784L11.251 0.882735C11.7668 0.168648 12.8934 0.638777 12.7486 1.50764L11.6666 7.99965H15.8701C16.55 7.99965 16.9437 8.76979 16.5457 9.32089L8.74883 20.1166C8.2331 20.8307 7.10646 20.3605 7.25127 19.4917L8.33327 12.9997H4.12974C3.44993 12.9997 3.05615 12.2295 3.45417 11.6784Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_683_608">
          <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </SvgIcon>
  );
};

export default Lightning;

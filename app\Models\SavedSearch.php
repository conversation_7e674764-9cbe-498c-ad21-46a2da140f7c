<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SavedSearch extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_email',
        'search_criteria',
        'name',
        'notification_frequency',
        'is_active',
        'last_notified_at',
        'results_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'search_criteria' => 'array',
        'is_active' => 'boolean',
        'last_notified_at' => 'datetime',
        'results_count' => 'integer',
    ];

    /**
     * Scope to get active searches
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get searches by email
     */
    public function scopeByEmail($query, $email)
    {
        return $query->where('user_email', $email);
    }

    /**
     * Scope to get searches due for notification
     */
    public function scopeDueForNotification($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('last_notified_at')
                  ->orWhere(function ($subQ) {
                      $subQ->where('notification_frequency', 'daily')
                           ->where('last_notified_at', '<=', Carbon::now()->subDay());
                  })
                  ->orWhere(function ($subQ) {
                      $subQ->where('notification_frequency', 'weekly')
                           ->where('last_notified_at', '<=', Carbon::now()->subWeek());
                  });
            });
    }

    /**
     * Execute the search and return matching cars
     */
    public function executeSearch()
    {
        $query = Car::query()->where('public_listing', true);

        foreach ($this->search_criteria as $field => $value) {
            if (empty($value)) continue;

            switch ($field) {
                case 'make':
                    $query->where('make', 'like', "%{$value}%");
                    break;
                case 'model':
                    $query->where('model', 'like', "%{$value}%");
                    break;
                case 'year_from':
                    $query->where('year', '>=', $value);
                    break;
                case 'year_to':
                    $query->where('year', '<=', $value);
                    break;
                case 'price_from':
                    $query->where('purchase_price', '>=', $value);
                    break;
                case 'price_to':
                    $query->where('purchase_price', '<=', $value);
                    break;
                case 'mileage_max':
                    $query->where('mileage', '<=', $value);
                    break;
                case 'fuel_type':
                    $query->where('fuel_type', $value);
                    break;
                case 'transmission':
                    $query->where('transmission', $value);
                    break;
            }
        }

        return $query->get();
    }

    /**
     * Update the results count
     */
    public function updateResultsCount()
    {
        $count = $this->executeSearch()->count();
        $this->update(['results_count' => $count]);
        return $count;
    }

    /**
     * Mark as notified
     */
    public function markAsNotified()
    {
        $this->update(['last_notified_at' => now()]);
    }

    /**
     * Get the search criteria as a readable string
     */
    public function getReadableCriteriaAttribute()
    {
        $criteria = [];

        foreach ($this->search_criteria as $field => $value) {
            if (empty($value)) continue;

            $label = match($field) {
                'make' => 'Make: ' . $value,
                'model' => 'Model: ' . $value,
                'year_from' => 'Year from: ' . $value,
                'year_to' => 'Year to: ' . $value,
                'price_from' => 'Price from: R' . number_format($value),
                'price_to' => 'Price to: R' . number_format($value),
                'mileage_max' => 'Max mileage: ' . number_format($value) . ' km',
                'fuel_type' => 'Fuel: ' . ucfirst($value),
                'transmission' => 'Transmission: ' . ucfirst($value),
                default => ucfirst($field) . ': ' . $value,
            };

            $criteria[] = $label;
        }

        return implode(', ', $criteria);
    }
}

<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $car->display_name }}
        </h2>
    </x-slot>
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $car->display_name }}</h1>
                <div class="flex items-center space-x-4 mt-2">
                    @if($car->status === 'pending_review')
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-clock mr-1"></i>
                            Pending Review
                        </span>
                    @elseif($car->status === 'active' && $car->public_listing)
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-check-circle mr-1"></i>
                            Live Listing
                        </span>
                        <a href="{{ route('public.cars.show', $car->slug) }}" target="_blank"
                           class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            View Public Listing
                        </a>
                    @else
                        <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-draft2digital mr-1"></i>
                            Draft
                        </span>
                    @endif
                </div>
            </div>

            <div class="flex space-x-3">
                <a href="{{ route('user-cars.index') }}"
                   class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Listings
                </a>

                @if($car->status === 'pending_review' || !$car->public_listing)
                    <a href="{{ route('user-cars.edit', $car) }}"
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Listing
                    </a>
                @endif
            </div>
        </div>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                {{ session('error') }}
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Images -->
                @if($car->carImages->count() > 0)
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Images</h2>

                        <!-- Main Image -->
                        <div class="mb-4">
                            <img src="{{ $car->carImages->first()->url }}"
                                 alt="{{ $car->display_name }}"
                                 class="w-full h-64 object-cover rounded-lg">
                        </div>

                        <!-- Thumbnail Gallery -->
                        @if($car->carImages->count() > 1)
                        <div class="grid grid-cols-4 gap-2">
                            @foreach($car->carImages->skip(1) as $image)
                                <img src="{{ $image->url }}"
                                     alt="{{ $car->display_name }}"
                                     class="w-full h-16 object-cover rounded cursor-pointer hover:opacity-75 transition-opacity"
                                     onclick="document.querySelector('.main-image').src = this.src">
                            @endforeach
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Description -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Description</h2>
                    <div class="prose max-w-none">
                        <p class="text-gray-700 whitespace-pre-line">{{ $car->public_description }}</p>
                    </div>
                </div>

                <!-- Features -->
                @if($car->features->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Features</h2>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        @foreach($car->features as $feature)
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-600"></i>
                                <span class="text-gray-700">{{ $feature->name }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Car Details -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Car Details</h2>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Price:</span>
                            <span class="font-semibold text-green-600">R{{ number_format($car->purchase_price) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Year:</span>
                            <span class="font-semibold">{{ $car->year }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Mileage:</span>
                            <span class="font-semibold">{{ number_format($car->mileage) }} km</span>
                        </div>

                        @if($car->color)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Color:</span>
                            <span class="font-semibold">{{ $car->color }}</span>
                        </div>
                        @endif

                        <div class="flex justify-between">
                            <span class="text-gray-600">Fuel Type:</span>
                            <span class="font-semibold">{{ ucfirst($car->fuel_type) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Transmission:</span>
                            <span class="font-semibold">{{ ucfirst($car->transmission) }}</span>
                        </div>

                        @if($car->body_type)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Body Type:</span>
                            <span class="font-semibold">{{ $car->body_type }}</span>
                        </div>
                        @endif

                        @if($car->engine_size)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Engine Size:</span>
                            <span class="font-semibold">{{ $car->engine_size }}</span>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Contact Information</h2>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Location:</span>
                            <span class="font-semibold">{{ $car->location }}</span>
                        </div>

                        @if($car->contact_phone)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Phone:</span>
                            <span class="font-semibold">{{ $car->contact_phone }}</span>
                        </div>
                        @endif

                        <div class="flex justify-between">
                            <span class="text-gray-600">Posted:</span>
                            <span class="font-semibold">{{ $car->created_at->format('M j, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Listing Status -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Listing Status</h2>

                    @if($car->status === 'pending_review')
                        <div class="text-center">
                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-clock text-2xl text-yellow-600"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Under Review</h3>
                            <p class="text-sm text-gray-600">Your listing is being reviewed by our team. We'll notify you once it's approved.</p>
                        </div>
                    @elseif($car->status === 'active' && $car->public_listing)
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-check-circle text-2xl text-green-600"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Live</h3>
                            <p class="text-sm text-gray-600">Your listing is live and visible to potential buyers.</p>
                        </div>
                    @else
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-draft2digital text-2xl text-gray-600"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Draft</h3>
                            <p class="text-sm text-gray-600">Your listing is saved as a draft and not yet visible to buyers.</p>
                        </div>
                    @endif
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Actions</h2>

                    <div class="space-y-3">
                        @if($car->status === 'pending_review' || !$car->public_listing)
                            <a href="{{ route('user-cars.edit', $car) }}"
                               class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors block">
                                <i class="fas fa-edit mr-2"></i>
                                Edit Listing
                            </a>
                        @endif

                        @if($car->status === 'active' && $car->public_listing)
                            <a href="{{ route('public.cars.show', $car->slug) }}" target="_blank"
                               class="w-full bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                View Public Listing
                            </a>
                        @endif

                        <form action="{{ route('user-cars.destroy', $car) }}" method="POST" class="w-full">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    onclick="return confirm('Are you sure you want to delete this listing? This action cannot be undone.')"
                                    class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Listing
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-app-layout>

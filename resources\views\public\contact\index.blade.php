@extends('public.layouts.app')

@section('title', 'Contact Us - I-fixit Cars')
@section('meta_description', 'Get in touch with I-fixit Cars. Contact us for inquiries about our premium car investment opportunities.')

@section('content')
<div class="bg-gray-50 min-h-screen py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Contact Us</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Ready to start your car investment journey? Get in touch with our expert team.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>

                @if(session('success'))
                    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
                        {{ session('success') }}
                    </div>
                @endif

                @if($errors->any())
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                        <ul class="list-disc list-inside">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form id="contactForm" action="{{ route('public.contact.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                            <input type="text" id="name" name="name" required
                                   value="{{ old('name') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required
                                   value="{{ old('email') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="phone" name="phone"
                                   value="{{ old('phone') }}"
                                   placeholder="+27 12 345 6789"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="inquiry_type" class="block text-sm font-medium text-gray-700 mb-2">Inquiry Type *</label>
                            <select id="inquiry_type" name="inquiry_type" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">Select inquiry type...</option>
                                <option value="general" {{ old('inquiry_type') == 'general' ? 'selected' : '' }}>General Inquiry</option>
                                <option value="financing" {{ old('inquiry_type') == 'financing' ? 'selected' : '' }}>Financing Options</option>
                                <option value="trade_in" {{ old('inquiry_type') == 'trade_in' ? 'selected' : '' }}>Trade-in Evaluation</option>
                                <option value="investment" {{ old('inquiry_type') == 'investment' ? 'selected' : '' }}>Investment Consultation</option>
                                <option value="other" {{ old('inquiry_type') == 'other' ? 'selected' : '' }}>Other</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                        <input type="text" id="subject" name="subject" required
                               value="{{ old('subject') }}"
                               placeholder="How can we help you?"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                        <textarea id="message" name="message" rows="6" required
                                  placeholder="Tell us more about your inquiry..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('message') }}</textarea>
                    </div>

                    <button type="submit" id="submitBtn"
                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="submit-text">Send Message</span>
                        <span class="loading-text hidden">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Sending...
                        </span>
                    </button>
                </form>
            </div>

            <!-- Contact Information -->
            <div class="space-y-8">
                <!-- Contact Details -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h2>

                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-map-marker-alt text-blue-600 text-xl mt-1"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Visit Our Showroom</h3>
                                <p class="text-gray-600">123 Business Street<br>Johannesburg, 2000<br>South Africa</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-phone text-blue-600 text-xl mt-1"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Call Us</h3>
                                <p class="text-gray-600">
                                    <a href="tel:+***********" class="hover:text-blue-600">+27 12 345 6789</a>
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-envelope text-blue-600 text-xl mt-1"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Email Us</h3>
                                <p class="text-gray-600">
                                    <a href="mailto:<EMAIL>" class="hover:text-blue-600"><EMAIL></a>
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-clock text-blue-600 text-xl mt-1"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Business Hours</h3>
                                <p class="text-gray-600">
                                    Monday - Friday: 8:00 - 17:00<br>
                                    Saturday: 8:00 - 13:00<br>
                                    Sunday: Closed
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-blue-50 rounded-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('public.cars.index') }}"
                           class="block bg-blue-600 text-white text-center py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            Browse Our Cars
                        </a>
                        <a href="{{ route('public.search.index') }}"
                           class="block bg-white text-blue-600 border border-blue-600 text-center py-3 px-4 rounded-lg hover:bg-blue-50 transition-colors">
                            Advanced Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-md w-full p-6">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-2xl text-green-600"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Message Sent!</h3>
            <p class="text-gray-600 mb-6">Thank you for contacting us. We'll get back to you within 24 hours.</p>
            <div class="flex space-x-3">
                <button onclick="closeSuccessModal()" class="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors">
                    Close
                </button>
                <button onclick="viewConfirmation()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                    View Details
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');
    let confirmationUrl = null;

    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading state
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        loadingText.classList.remove('hidden');

        // Clear previous errors
        clearErrors();

        // Prepare form data
        const formData = new FormData(contactForm);

        // Send AJAX request
        fetch(contactForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store confirmation URL
                confirmationUrl = data.redirect_url;

                // Reset form
                contactForm.reset();

                // Show success modal
                showSuccessModal();
            } else {
                // Show errors
                showErrors(data.errors);
                showToast(data.message || 'Please check your input and try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitText.classList.remove('hidden');
            loadingText.classList.add('hidden');
        });
    });

    function showErrors(errors) {
        // Clear previous errors
        clearErrors();

        // Show field-specific errors
        Object.keys(errors).forEach(field => {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('border-red-500');

                // Create error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'text-red-600 text-sm mt-1';
                errorDiv.textContent = errors[field][0];

                // Insert after input
                input.parentNode.insertBefore(errorDiv, input.nextSibling);
            }
        });
    }

    function clearErrors() {
        // Remove error styling
        document.querySelectorAll('.border-red-500').forEach(el => {
            el.classList.remove('border-red-500');
        });

        // Remove error messages
        document.querySelectorAll('.text-red-600').forEach(el => {
            if (el.classList.contains('text-sm')) {
                el.remove();
            }
        });
    }

    function showSuccessModal() {
        document.getElementById('successModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    window.closeSuccessModal = function() {
        document.getElementById('successModal').classList.add('hidden');
        document.body.style.overflow = '';
    };

    window.viewConfirmation = function() {
        if (confirmationUrl) {
            window.location.href = confirmationUrl;
        } else {
            closeSuccessModal();
        }
    };
});
</script>
@endsection

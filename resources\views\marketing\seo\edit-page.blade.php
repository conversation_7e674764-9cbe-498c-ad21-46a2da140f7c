<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit SEO Settings') }} - {{ ucfirst(str_replace('-', ' ', $page)) }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">SEO Settings</h1>
                        <p class="mt-2 text-gray-600">Configure SEO metadata for {{ ucfirst(str_replace('-', ' ', $page)) }}</p>
                    </div>
                    <a href="{{ route('marketing.seo.pages') }}" 
                       class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Pages
                    </a>
                </div>
            </div>

            <!-- SEO Form -->
            <div class="bg-white rounded-lg shadow">
                <form action="{{ route('marketing.seo.pages.update', $page) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="p-6 space-y-6">
                        <!-- Basic SEO Settings -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic SEO Settings</h3>
                            
                            <!-- Title Tag -->
                            <div class="mb-4">
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Page Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $seoSettings['title']) }}"
                                       maxlength="60"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Enter page title (max 60 characters)"
                                       required>
                                <div class="mt-1 flex justify-between text-xs text-gray-500">
                                    <span>This appears in search results and browser tabs</span>
                                    <span id="title-count">0/60</span>
                                </div>
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Meta Description -->
                            <div class="mb-4">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Meta Description <span class="text-red-500">*</span>
                                </label>
                                <textarea id="description" 
                                          name="description" 
                                          rows="3"
                                          maxlength="160"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="Enter meta description (max 160 characters)"
                                          required>{{ old('description', $seoSettings['description']) }}</textarea>
                                <div class="mt-1 flex justify-between text-xs text-gray-500">
                                    <span>This appears in search results below the title</span>
                                    <span id="description-count">0/160</span>
                                </div>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Keywords -->
                            <div class="mb-4">
                                <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                    Keywords
                                </label>
                                <input type="text" 
                                       id="keywords" 
                                       name="keywords" 
                                       value="{{ old('keywords', $seoSettings['keywords']) }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="luxury cars, premium vehicles, AutoLux">
                                <div class="mt-1 text-xs text-gray-500">
                                    Separate keywords with commas. Focus on 3-5 relevant keywords.
                                </div>
                                @error('keywords')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Open Graph Settings -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Social Media (Open Graph)</h3>
                            
                            <!-- OG Title -->
                            <div class="mb-4">
                                <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Social Media Title
                                </label>
                                <input type="text" 
                                       id="og_title" 
                                       name="og_title" 
                                       value="{{ old('og_title', $seoSettings['og_title']) }}"
                                       maxlength="60"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Leave blank to use page title">
                                <div class="mt-1 text-xs text-gray-500">
                                    Title when shared on social media (Facebook, Twitter, etc.)
                                </div>
                                @error('og_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- OG Description -->
                            <div class="mb-4">
                                <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Social Media Description
                                </label>
                                <textarea id="og_description" 
                                          name="og_description" 
                                          rows="3"
                                          maxlength="160"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="Leave blank to use meta description">{{ old('og_description', $seoSettings['og_description']) }}</textarea>
                                <div class="mt-1 text-xs text-gray-500">
                                    Description when shared on social media
                                </div>
                                @error('og_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- OG Image -->
                            <div class="mb-4">
                                <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Social Media Image URL
                                </label>
                                <input type="url" 
                                       id="og_image" 
                                       name="og_image" 
                                       value="{{ old('og_image', $seoSettings['og_image']) }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="https://example.com/image.jpg">
                                <div class="mt-1 text-xs text-gray-500">
                                    Image displayed when shared on social media (1200x630px recommended)
                                </div>
                                @error('og_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="px-6 py-4 bg-gray-50 border-t flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            <i class="fas fa-info-circle mr-1"></i>
                            Changes will be applied immediately to the live website
                        </div>
                        <div class="flex space-x-3">
                            <a href="{{ route('marketing.seo.pages') }}" 
                               class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>
                                Save SEO Settings
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- SEO Preview -->
            <div class="mt-8 bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Search Result Preview</h3>
                <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div id="seo-preview">
                        <div class="text-blue-600 text-lg hover:underline cursor-pointer" id="preview-title">
                            {{ $seoSettings['title'] }}
                        </div>
                        <div class="text-green-700 text-sm mt-1" id="preview-url">
                            https://autolux.com{{ request()->route('page') === 'home' ? '' : '/' . str_replace('-', '/', request()->route('page')) }}
                        </div>
                        <div class="text-gray-600 text-sm mt-1" id="preview-description">
                            {{ $seoSettings['description'] }}
                        </div>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    This is how your page will appear in Google search results
                </div>
            </div>
        </div>
    </div>

    <!-- Character Counter Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Character counters
            const titleInput = document.getElementById('title');
            const titleCount = document.getElementById('title-count');
            const descriptionInput = document.getElementById('description');
            const descriptionCount = document.getElementById('description-count');
            
            // Preview elements
            const previewTitle = document.getElementById('preview-title');
            const previewDescription = document.getElementById('preview-description');
            
            function updateTitleCounter() {
                const length = titleInput.value.length;
                titleCount.textContent = `${length}/60`;
                titleCount.className = length > 60 ? 'text-red-500' : 'text-gray-500';
                previewTitle.textContent = titleInput.value || 'Page Title';
            }
            
            function updateDescriptionCounter() {
                const length = descriptionInput.value.length;
                descriptionCount.textContent = `${length}/160`;
                descriptionCount.className = length > 160 ? 'text-red-500' : 'text-gray-500';
                previewDescription.textContent = descriptionInput.value || 'Meta description will appear here';
            }
            
            titleInput.addEventListener('input', updateTitleCounter);
            descriptionInput.addEventListener('input', updateDescriptionCounter);
            
            // Initialize counters
            updateTitleCounter();
            updateDescriptionCounter();
        });
    </script>
</x-app-layout>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('saved_searches', function (Blueprint $table) {
            $table->id();
            $table->string('user_email');
            $table->json('search_criteria');
            $table->string('name', 100)->nullable();
            $table->enum('notification_frequency', ['immediate', 'daily', 'weekly'])->default('weekly');
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_notified_at')->nullable();
            $table->integer('results_count')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['user_email', 'is_active']);
            $table->index(['notification_frequency', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('saved_searches');
    }
};

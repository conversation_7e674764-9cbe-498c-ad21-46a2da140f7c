<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Car;
use App\Models\CarImage;
use App\Models\CarFeature;
use App\Models\User;

echo "Creating demo cars for public frontend...\n";

// Get or create a user for the cars
$user = User::first();
if (!$user) {
    echo "No users found. Creating demo user...\n";
    $user = User::create([
        'name' => 'Demo User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
    ]);
}

// Sample cars data
$carsData = [
    [
        'make' => 'BMW',
        'model' => '3 Series',
        'variant' => '320i',
        'year' => 2020,
        'mileage' => 45000,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'color' => 'Alpine White',
        'body_type' => 'sedan',
        'purchase_price' => 450000,
        'estimated_market_value' => 520000,
        'current_phase' => 'dealership',
        'status' => 'active',
        'public_listing' => true,
        'featured' => true,
        'listing_priority' => 10,
        'meta_title' => '2020 BMW 3 Series 320i - Premium Investment Opportunity',
        'meta_description' => 'Excellent condition BMW 3 Series with low mileage and strong investment potential.',
        'public_description' => 'This stunning BMW 3 Series represents an excellent investment opportunity. With its timeless design, proven reliability, and strong resale value, this vehicle offers both driving pleasure and financial returns. The car has been meticulously maintained and comes with a comprehensive service history.',
        'key_features' => ['LED Headlights', 'Leather Seats', 'Navigation System', 'Bluetooth Connectivity'],
    ],
    [
        'make' => 'Mercedes-Benz',
        'model' => 'C-Class',
        'variant' => 'C200',
        'year' => 2019,
        'mileage' => 38000,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'color' => 'Obsidian Black',
        'body_type' => 'sedan',
        'purchase_price' => 420000,
        'estimated_market_value' => 480000,
        'current_phase' => 'dealership',
        'status' => 'active',
        'public_listing' => true,
        'featured' => true,
        'listing_priority' => 9,
        'meta_title' => '2019 Mercedes-Benz C-Class C200 - Luxury Investment',
        'meta_description' => 'Premium Mercedes-Benz C-Class with excellent condition and investment potential.',
        'public_description' => 'Experience luxury and investment potential with this pristine Mercedes-Benz C-Class. Known for its exceptional build quality and strong market performance, this vehicle represents a smart investment choice for discerning buyers.',
        'key_features' => ['Premium Sound System', 'Climate Control', 'Alloy Wheels', 'Parking Sensors'],
    ],
    [
        'make' => 'Audi',
        'model' => 'A4',
        'variant' => '2.0T',
        'year' => 2021,
        'mileage' => 25000,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'color' => 'Glacier White',
        'body_type' => 'sedan',
        'purchase_price' => 520000,
        'estimated_market_value' => 580000,
        'current_phase' => 'dealership',
        'status' => 'active',
        'public_listing' => true,
        'featured' => true,
        'listing_priority' => 8,
        'meta_title' => '2021 Audi A4 2.0T - Modern Investment Vehicle',
        'meta_description' => 'Nearly new Audi A4 with advanced technology and strong appreciation potential.',
        'public_description' => 'This nearly new Audi A4 combines cutting-edge technology with proven investment value. Perfect for those seeking modern luxury with financial returns, this vehicle showcases Audi\'s commitment to innovation and quality.',
        'key_features' => ['Digital Dashboard', 'Apple CarPlay', 'Heated Seats', 'Sunroof'],
    ],
    [
        'make' => 'Toyota',
        'model' => 'Camry',
        'variant' => 'Hybrid',
        'year' => 2022,
        'mileage' => 15000,
        'fuel_type' => 'hybrid',
        'transmission' => 'automatic',
        'color' => 'Pearl White',
        'body_type' => 'sedan',
        'purchase_price' => 380000,
        'estimated_market_value' => 420000,
        'current_phase' => 'dealership',
        'status' => 'active',
        'public_listing' => true,
        'featured' => false,
        'listing_priority' => 7,
        'meta_title' => '2022 Toyota Camry Hybrid - Eco-Friendly Investment',
        'meta_description' => 'Fuel-efficient Toyota Camry Hybrid with excellent reliability and resale value.',
        'public_description' => 'Embrace the future with this eco-friendly Toyota Camry Hybrid. Exceptional fuel economy meets proven Toyota reliability and strong resale value, making this an ideal investment for environmentally conscious buyers.',
        'key_features' => ['Hybrid Engine', 'Safety Sense', 'Wireless Charging', 'LED Headlights'],
    ],
    [
        'make' => 'Volkswagen',
        'model' => 'Golf',
        'variant' => 'GTI',
        'year' => 2020,
        'mileage' => 32000,
        'fuel_type' => 'petrol',
        'transmission' => 'manual',
        'color' => 'Tornado Red',
        'body_type' => 'hatchback',
        'purchase_price' => 350000,
        'estimated_market_value' => 390000,
        'current_phase' => 'dealership',
        'status' => 'active',
        'public_listing' => true,
        'featured' => false,
        'listing_priority' => 6,
        'meta_title' => '2020 Volkswagen Golf GTI - Performance Investment',
        'meta_description' => 'Sporty Golf GTI with manual transmission and strong enthusiast appeal.',
        'public_description' => 'This Golf GTI offers the perfect blend of everyday usability and weekend excitement. Strong enthusiast following ensures excellent resale value, making it a smart choice for performance car investors.',
        'key_features' => ['Turbo Engine', 'Sport Suspension', 'Manual Transmission', 'Performance Brakes'],
    ],
    [
        'make' => 'Honda',
        'model' => 'Civic',
        'variant' => 'Type R',
        'year' => 2021,
        'mileage' => 18000,
        'fuel_type' => 'petrol',
        'transmission' => 'manual',
        'color' => 'Championship White',
        'body_type' => 'hatchback',
        'purchase_price' => 480000,
        'estimated_market_value' => 550000,
        'current_phase' => 'dealership',
        'status' => 'active',
        'public_listing' => true,
        'featured' => false,
        'listing_priority' => 5,
        'meta_title' => '2021 Honda Civic Type R - Collector Investment',
        'meta_description' => 'Rare Honda Civic Type R with collector potential and strong performance credentials.',
        'public_description' => 'A true collector\'s item, this Civic Type R represents the pinnacle of Honda\'s engineering. Limited production and high demand make it an excellent investment for those seeking both performance and appreciation potential.',
        'key_features' => ['Turbo Engine', 'Limited Slip Differential', 'Sport Mode', 'Performance Tires'],
    ],
];

echo "Creating " . count($carsData) . " demo cars...\n";

foreach ($carsData as $index => $carData) {
    // Add user_id to car data
    $carData['user_id'] = $user->id;
    $carData['created_by'] = $user->id;
    $carData['updated_by'] = $user->id;
    
    $car = Car::create($carData);
    echo "Created: {$car->year} {$car->make} {$car->model} (ID: {$car->id})\n";
    
    // Create sample car images
    $imageTypes = ['exterior', 'interior', 'engine'];
    foreach ($imageTypes as $imgIndex => $type) {
        try {
            CarImage::create([
                'car_id' => $car->id,
                'image_path' => "car_images/sample_{$car->id}_{$type}.jpg",
                'image_type' => $type,
                'sort_order' => $imgIndex,
                'alt_text' => "{$car->year} {$car->make} {$car->model} {$type} view",
                'is_primary' => $imgIndex === 0,
                'caption' => ucfirst($type) . ' view of ' . $car->display_name,
            ]);
        } catch (Exception $e) {
            echo "  Warning: Could not create image for {$car->display_name}: " . $e->getMessage() . "\n";
        }
    }
    
    // Attach random features if they exist
    $features = CarFeature::inRandomOrder()->limit(rand(5, 8))->get();
    if ($features->count() > 0) {
        $car->features()->attach($features->pluck('id'));
        echo "  Added {$features->count()} features\n";
    }
}

echo "\nDemo cars created successfully!\n";
echo "Total cars: " . Car::count() . "\n";
echo "Public cars: " . Car::where('public_listing', true)->count() . "\n";
echo "Featured cars: " . Car::where('featured', true)->count() . "\n";

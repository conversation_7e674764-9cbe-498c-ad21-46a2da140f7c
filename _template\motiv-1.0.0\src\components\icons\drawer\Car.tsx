import { SvgIcon, SvgIconProps } from '@mui/material';

const Car = (props: SvgIconProps) => {
  return (
    <SvgIcon width="18" height="16" viewBox="0 0 18 16" fill="none" {...props}>
      <path
        d="M16.3098 5.63L15.1698 2.20917C15.0045 1.71124 14.6864 1.27808 14.2608 0.971257C13.8352 0.66443 13.3237 0.499535 12.799 0.500001H5.20067C4.67599 0.499535 4.16452 0.66443 3.73891 0.971257C3.3133 1.27808 2.99521 1.71124 2.82984 2.20917L1.68984 5.63C1.38701 5.75707 1.1284 5.97063 0.946375 6.24397C0.764348 6.51731 0.666995 6.83827 0.666504 7.16667V11.3333C0.666504 11.9608 1.019 12.5017 1.53234 12.7858C1.5215 12.8408 1.49984 12.8908 1.49984 12.9483V14.6667C1.49984 14.8877 1.58763 15.0996 1.74391 15.2559C1.9002 15.4122 2.11216 15.5 2.33317 15.5H3.1665C3.38752 15.5 3.59948 15.4122 3.75576 15.2559C3.91204 15.0996 3.99984 14.8877 3.99984 14.6667V13H13.9998V14.6667C13.9998 14.8877 14.0876 15.0996 14.2439 15.2559C14.4002 15.4122 14.6122 15.5 14.8332 15.5H15.6665C15.8875 15.5 16.0995 15.4122 16.2558 15.2559C16.412 15.0996 16.4998 14.8877 16.4998 14.6667V12.9483C16.4998 12.8908 16.4782 12.84 16.4673 12.7858C16.7285 12.6434 16.9466 12.4335 17.099 12.178C17.2513 11.9225 17.3321 11.6308 17.3332 11.3333V7.16667C17.3332 6.47583 16.9098 5.8825 16.3098 5.63ZM2.33317 11.3333V7.16667H15.6665L15.6682 11.3333H2.33317ZM5.20067 2.16667H12.7982C13.1573 2.16667 13.4757 2.395 13.589 2.73667L14.5107 5.5H3.489L4.40984 2.73667C4.46513 2.57066 4.57127 2.42627 4.71321 2.32397C4.85515 2.22166 5.0257 2.16663 5.20067 2.16667Z"
        fill="currentColor"
      />
      <path
        d="M4.4165 10.5C5.10686 10.5 5.6665 9.94036 5.6665 9.25C5.6665 8.55964 5.10686 8 4.4165 8C3.72615 8 3.1665 8.55964 3.1665 9.25C3.1665 9.94036 3.72615 10.5 4.4165 10.5Z"
        fill="currentColor"
      />
      <path
        d="M13.583 10.5C14.2734 10.5 14.833 9.94036 14.833 9.25C14.833 8.55964 14.2734 8 13.583 8C12.8927 8 12.333 8.55964 12.333 9.25C12.333 9.94036 12.8927 10.5 13.583 10.5Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Car;

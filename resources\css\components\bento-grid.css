/**
 * Bento Grid System
 * Modular grid layout inspired by Japanese bento boxes
 */

/* Base Bento Grid Container */
.bento-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-auto-rows: minmax(200px, auto);
    gap: 1.5rem;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Responsive Grid Adjustments */
@media (min-width: 768px) {
    .bento-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .bento-grid {
        grid-template-columns: repeat(4, 1fr);
        grid-auto-rows: minmax(240px, auto);
    }
}

@media (min-width: 1280px) {
    .bento-grid {
        grid-template-columns: repeat(6, 1fr);
        grid-auto-rows: minmax(260px, auto);
    }
}

/* Bento Grid Items */
.bento-item {
    background: white;
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.bento-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Size Variants */
.bento-item--small {
    grid-column: span 1;
    grid-row: span 1;
}

.bento-item--medium {
    grid-column: span 2;
    grid-row: span 1;
}

.bento-item--large {
    grid-column: span 2;
    grid-row: span 2;
}

.bento-item--wide {
    grid-column: span 3;
    grid-row: span 1;
}

.bento-item--tall {
    grid-column: span 1;
    grid-row: span 2;
}

.bento-item--extra-large {
    grid-column: span 3;
    grid-row: span 2;
}

/* Mobile Responsive - All items become single column */
@media (max-width: 767px) {
    .bento-item--large,
    .bento-item--wide,
    .bento-item--tall,
    .bento-item--extra-large,
    .bento-item--medium {
        grid-column: span 1;
        grid-row: span 1;
    }
    
    .bento-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }
}

/* Content Types */
.bento-item--featured-car {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.bento-item--stats {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    text-align: center;
}

.bento-item--testimonial {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.bento-item--cta {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: #1a202c;
    text-align: center;
}

.bento-item--info {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: #1a202c;
}

.bento-item--investment {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #1a202c;
}

/* Content Styling */
.bento-item__header {
    margin-bottom: 1rem;
}

.bento-item__title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.bento-item__subtitle {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.bento-item__content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.bento-item__footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Special Elements */
.bento-item__icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.bento-item__number {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.bento-item__image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 1rem;
    margin-bottom: 1rem;
}

.bento-item__button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.bento-item__button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Animation Classes */
.bento-item--animate-in {
    animation: bentoSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes bentoSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Staggered Animation */
.bento-item:nth-child(1) { animation-delay: 0ms; }
.bento-item:nth-child(2) { animation-delay: 100ms; }
.bento-item:nth-child(3) { animation-delay: 200ms; }
.bento-item:nth-child(4) { animation-delay: 300ms; }
.bento-item:nth-child(5) { animation-delay: 400ms; }
.bento-item:nth-child(6) { animation-delay: 500ms; }
.bento-item:nth-child(7) { animation-delay: 600ms; }
.bento-item:nth-child(8) { animation-delay: 700ms; }

/* Hover Effects */
.bento-item--interactive {
    cursor: pointer;
}

.bento-item--interactive:hover .bento-item__icon {
    transform: scale(1.1) rotate(5deg);
    transition: transform 0.3s ease;
}

.bento-item--interactive:hover .bento-item__title {
    transform: translateX(5px);
    transition: transform 0.3s ease;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bento-item {
        background: #1f2937;
        color: #f9fafb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }
    
    .bento-item:hover {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }
}

/* Accessibility */
.bento-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .bento-item,
    .bento-item__icon,
    .bento-item__title,
    .bento-item__button {
        transition: none;
    }
    
    .bento-item--animate-in {
        animation: none;
        opacity: 1;
        transform: none;
    }
    
    .bento-item:hover {
        transform: none;
    }
}

/* Print Styles */
@media print {
    .bento-grid {
        display: block;
        padding: 0;
    }
    
    .bento-item {
        break-inside: avoid;
        margin-bottom: 1rem;
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}

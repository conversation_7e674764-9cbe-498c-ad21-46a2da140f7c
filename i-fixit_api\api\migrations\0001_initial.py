# Generated by Django 4.2.21 on 2025-05-12 21:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Car',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('make', models.CharField(max_length=100)),
                ('model', models.CharField(max_length=100)),
                ('year', models.IntegerField()),
                ('vin', models.CharField(blank=True, max_length=100, null=True)),
                ('registration_number', models.CharField(blank=True, max_length=50, null=True)),
                ('color', models.CharField(blank=True, max_length=50, null=True)),
                ('body_type', models.Char<PERSON><PERSON>(max_length=50)),
                ('engine_size', models.CharField(blank=True, max_length=50, null=True)),
                ('fuel_type', models.Char<PERSON>ield(max_length=50)),
                ('transmission', models.CharField(max_length=50)),
                ('mileage', models.IntegerField()),
                ('features', models.JSONField(blank=True, null=True)),
                ('purchase_date', models.DateField()),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('auction_house', models.CharField(blank=True, max_length=100, null=True)),
                ('auction_lot_number', models.CharField(blank=True, max_length=50, null=True)),
                ('damage_description', models.TextField()),
                ('damage_severity', models.CharField(choices=[('light', 'Light'), ('moderate', 'Moderate'), ('severe', 'Severe')], default='moderate', max_length=20)),
                ('operational_status', models.CharField(choices=[('running', 'Running'), ('non_running', 'Non-running')], default='running', max_length=20)),
                ('current_phase', models.CharField(choices=[('bidding', 'Bidding'), ('fixing', 'Fixing'), ('dealership', 'Dealership'), ('sold', 'Sold')], default='bidding', max_length=20)),
                ('repair_start_date', models.DateField(blank=True, null=True)),
                ('repair_end_date', models.DateField(blank=True, null=True)),
                ('dealership_date', models.DateField(blank=True, null=True)),
                ('sale_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cars', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Opportunity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source', models.CharField(max_length=100)),
                ('listing_url', models.URLField()),
                ('make', models.CharField(max_length=100)),
                ('model', models.CharField(max_length=100)),
                ('year', models.IntegerField()),
                ('auction_end_date', models.DateTimeField(blank=True, null=True)),
                ('current_bid', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('damage_description', models.TextField(blank=True, null=True)),
                ('image_urls', models.JSONField(blank=True, null=True)),
                ('estimated_repair_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('estimated_market_value', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('potential_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('opportunity_score', models.IntegerField()),
                ('status', models.CharField(choices=[('new', 'New'), ('viewed', 'Viewed'), ('interested', 'Interested'), ('bidding', 'Bidding'), ('won', 'Won'), ('lost', 'Lost'), ('expired', 'Expired')], default='new', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_makes', models.JSONField(blank=True, null=True)),
                ('preferred_models', models.JSONField(blank=True, null=True)),
                ('min_year', models.IntegerField(blank=True, null=True)),
                ('max_year', models.IntegerField(blank=True, null=True)),
                ('min_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('max_investment', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('notification_email', models.BooleanField(default=True)),
                ('notification_sms', models.BooleanField(default=False)),
                ('notification_app', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('buyer_name', models.CharField(blank=True, max_length=100, null=True)),
                ('buyer_contact', models.CharField(blank=True, max_length=100, null=True)),
                ('sale_date', models.DateField()),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('dealership_commission', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sale', to='api.car')),
            ],
        ),
        migrations.CreateModel(
            name='Part',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('condition', models.CharField(choices=[('new', 'New'), ('used', 'Used'), ('refurbished', 'Refurbished')], default='new', max_length=20)),
                ('quantity', models.IntegerField(default=1)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('purchase_date', models.DateField()),
                ('installation_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parts', to='api.car')),
                ('supplier', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parts', to='api.supplier')),
            ],
        ),
        migrations.CreateModel(
            name='Painting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=255)),
                ('areas_painted', models.TextField()),
                ('paint_type', models.CharField(max_length=100)),
                ('paint_color', models.CharField(max_length=100)),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=12)),
                ('date', models.DateField()),
                ('painter_name', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='painting', to='api.car')),
            ],
        ),
        migrations.CreateModel(
            name='Labor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=255)),
                ('hours', models.DecimalField(decimal_places=2, max_digits=6)),
                ('rate_per_hour', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=12)),
                ('date', models.DateField()),
                ('mechanic_name', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='labor', to='api.car')),
            ],
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(max_length=100)),
                ('file', models.FileField(upload_to='car_documents/')),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('upload_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='api.car')),
            ],
        ),
        migrations.CreateModel(
            name='DamagedPart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('part_name', models.CharField(max_length=100)),
                ('part_location', models.CharField(max_length=100)),
                ('damage_description', models.TextField()),
                ('estimated_repair_cost', models.DecimalField(decimal_places=2, max_digits=12)),
                ('needs_replacement', models.BooleanField(default=False)),
                ('image', models.ImageField(blank=True, null=True, upload_to='damaged_parts/')),
                ('is_repaired', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='damaged_parts', to='api.car')),
            ],
        ),
        migrations.CreateModel(
            name='CarImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='car_images/')),
                ('image_type', models.CharField(choices=[('before_repair', 'Before Repair'), ('during_repair', 'During Repair'), ('after_repair', 'After Repair'), ('damage', 'Damage'), ('other', 'Other')], default='other', max_length=20)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='api.car')),
            ],
        ),
    ]

@extends('public.layouts.app')

@section('title', 'Browse Cars - I-fixit Cars')
@section('meta_description', 'Browse our premium collection of investment-grade vehicles. Find your perfect car with advanced search and filtering options.')

@section('content')
<!-- Page Header -->
<section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-4"
                data-kinetic-text data-animation-type="reveal" data-split-by="words" data-stagger="100">
                Browse Premium Cars
            </h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto"
               data-kinetic-text data-animation-type="typewriter" data-split-by="chars" data-delay="500" data-stagger="20">
                Discover your next investment opportunity from our curated collection of quality vehicles.
            </p>
        </div>
    </div>
</section>

<!-- Search and Filters -->
<section class="bg-white border-b border-gray-200 sticky top-16 z-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form method="GET" class="space-y-4">
            <!-- Quick Search -->
            <div class="flex flex-col lg:flex-row gap-4">
                <div class="flex-1 relative">
                    <input type="text"
                           id="searchInput"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Search by make, model, or year..."
                           autocomplete="off"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">

                    <!-- Search Suggestions Dropdown -->
                    <div id="searchSuggestions" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg mt-1 hidden z-10 max-h-60 overflow-y-auto">
                        <!-- Suggestions will be populated by JavaScript -->
                    </div>
                </div>
                <div class="flex gap-2">
                    <button type="submit"
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                    <button type="button" id="toggle-filters"
                            class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-filter mr-2"></i>Filters
                    </button>
                </div>
            </div>

            <!-- Advanced Filters (Hidden by default) -->
            <div id="advanced-filters" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
                <select name="make" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">Any Make</option>
                    @foreach($filterOptions['makes'] as $make)
                        <option value="{{ $make }}" {{ request('make') === $make ? 'selected' : '' }}>
                            {{ $make }}
                        </option>
                    @endforeach
                </select>

                <select name="fuel_type" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">Any Fuel Type</option>
                    @foreach($filterOptions['fuel_types'] as $fuelType)
                        <option value="{{ $fuelType }}" {{ request('fuel_type') === $fuelType ? 'selected' : '' }}>
                            {{ ucfirst($fuelType) }}
                        </option>
                    @endforeach
                </select>

                <select name="transmission" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">Any Transmission</option>
                    @foreach($filterOptions['transmissions'] as $transmission)
                        <option value="{{ $transmission }}" {{ request('transmission') === $transmission ? 'selected' : '' }}>
                            {{ ucfirst($transmission) }}
                        </option>
                    @endforeach
                </select>

                <select name="sort" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="priority" {{ request('sort') === 'priority' ? 'selected' : '' }}>Recommended</option>
                    <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                    <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                    <option value="year_new" {{ request('sort') === 'year_new' ? 'selected' : '' }}>Year: Newest First</option>
                    <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                </select>
            </div>

            <!-- Applied Filters -->
            @if(count($appliedFilters) > 0)
            <div class="flex flex-wrap gap-2 pt-2">
                <span class="text-sm text-gray-600">Active filters:</span>
                @foreach($appliedFilters as $key => $value)
                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                    {{ ucfirst($key) }}: {{ $value }}
                    <button type="button" onclick="removeFilter('{{ $key }}')" class="ml-2 text-blue-600 hover:text-blue-800">
                        <i class="fas fa-times"></i>
                    </button>
                </span>
                @endforeach
                <a href="{{ route('public.cars.index') }}" class="text-sm text-gray-500 hover:text-gray-700 underline">
                    Clear all
                </a>
            </div>
            @endif
        </form>
    </div>
</section>

<!-- Results -->
<section class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Results Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    {{ $cars->total() }} Cars Found
                </h2>
                @if(request('search'))
                <p class="text-gray-600">Results for "{{ request('search') }}"</p>
                @endif
            </div>

            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">View:</span>
                <button id="grid-view" class="p-2 text-gray-600 hover:text-blue-600 border rounded">
                    <i class="fas fa-th-large"></i>
                </button>
                <button id="list-view" class="p-2 text-gray-600 hover:text-blue-600 border rounded">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>

        <!-- Car Grid -->
        @if($cars->count() > 0)
        <div id="cars-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($cars as $car)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                 data-scroll-animation="scale-in" data-animation-delay="{{ $loop->index * 100 }}">
                <div class="relative">
                    <img src="{{ $car->primary_image_url }}"
                         alt="{{ $car->display_name }}"
                         class="w-full h-48 object-cover">

                    @if($car->featured)
                    <div class="absolute top-4 left-4">
                        <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            Featured
                        </span>
                    </div>
                    @endif

                    @if($car->investment_score)
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            {{ $car->investment_score }}
                        </span>
                    </div>
                    @endif

                    <div class="absolute bottom-4 right-4">
                        <button class="bg-white bg-opacity-90 text-gray-700 p-2 rounded-full hover:bg-opacity-100 transition-all">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                </div>

                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $car->display_name }}</h3>

                    <div class="flex justify-between items-center mb-4">
                        <span class="text-2xl font-bold text-blue-600">R{{ number_format($car->purchase_price) }}</span>
                        @if($car->projected_roi_percentage)
                        <span class="text-green-600 font-semibold">+{{ number_format($car->projected_roi_percentage, 1) }}% ROI</span>
                        @endif
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                        <div><i class="fas fa-calendar mr-1"></i> {{ $car->year }}</div>
                        <div><i class="fas fa-tachometer-alt mr-1"></i> {{ number_format($car->mileage) }} km</div>
                        <div><i class="fas fa-gas-pump mr-1"></i> {{ ucfirst($car->fuel_type) }}</div>
                        <div><i class="fas fa-cogs mr-1"></i> {{ ucfirst($car->transmission) }}</div>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{{ route('public.cars.show', $car) }}"
                           class="flex-1 bg-blue-600 text-white text-center py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            View Details
                        </a>
                        <button class="bg-gray-100 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-phone"></i>
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $cars->links() }}
        </div>
        @else
        <!-- No Results -->
        <div class="text-center py-12">
            <i class="fas fa-car text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No cars found</h3>
            <p class="text-gray-600 mb-6">Try adjusting your search criteria or browse all cars.</p>
            <a href="{{ route('public.cars.index') }}"
               class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                View All Cars
            </a>
        </div>
        @endif
    </div>
</section>
@endsection

@push('scripts')
<script src="{{ asset('js/animations/kinetic-typography.js') }}"></script>
<script src="{{ asset('js/animations/micro-interactions.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle filters
    const toggleFilters = document.getElementById('toggle-filters');
    const advancedFilters = document.getElementById('advanced-filters');

    toggleFilters.addEventListener('click', function() {
        advancedFilters.classList.toggle('hidden');
        const icon = this.querySelector('i');
        if (advancedFilters.classList.contains('hidden')) {
            icon.className = 'fas fa-filter mr-2';
        } else {
            icon.className = 'fas fa-filter-circle-xmark mr-2';
        }
    });

    // View toggle
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const carsGrid = document.getElementById('cars-grid');

    gridView.addEventListener('click', function() {
        carsGrid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
        this.classList.add('text-blue-600', 'bg-blue-50');
        listView.classList.remove('text-blue-600', 'bg-blue-50');
    });

    listView.addEventListener('click', function() {
        carsGrid.className = 'space-y-4';
        this.classList.add('text-blue-600', 'bg-blue-50');
        gridView.classList.remove('text-blue-600', 'bg-blue-50');
    });

    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Search suggestions functionality
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    let searchTimeout;

    if (searchInput && searchSuggestions) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                searchSuggestions.classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(() => {
                fetchSearchSuggestions(query);
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            const query = this.value.trim();
            if (query.length >= 2) {
                fetchSearchSuggestions(query);
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
                searchSuggestions.classList.add('hidden');
            }
        });
    }

    async function fetchSearchSuggestions(query) {
        try {
            const response = await fetch(`{{ route('public.search.suggestions') }}?q=${encodeURIComponent(query)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            });

            if (response.ok) {
                const data = await response.json();
                displaySearchSuggestions(data.suggestions);
            }
        } catch (error) {
            console.error('Error fetching search suggestions:', error);
        }
    }

    function displaySearchSuggestions(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            searchSuggestions.classList.add('hidden');
            return;
        }

        const html = suggestions.map(suggestion => `
            <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                 onclick="selectSuggestion('${suggestion.text}')">
                <div class="flex items-center">
                    <i class="fas ${suggestion.icon} text-gray-400 mr-3"></i>
                    <div>
                        <div class="font-medium text-gray-900">${suggestion.text}</div>
                        ${suggestion.subtitle ? `<div class="text-sm text-gray-500">${suggestion.subtitle}</div>` : ''}
                    </div>
                </div>
            </div>
        `).join('');

        searchSuggestions.innerHTML = html;
        searchSuggestions.classList.remove('hidden');
    }

    function selectSuggestion(text) {
        searchInput.value = text;
        searchSuggestions.classList.add('hidden');
        searchInput.form.submit();
    }
});

function removeFilter(filterName) {
    const url = new URL(window.location);
    url.searchParams.delete(filterName);
    window.location = url.toString();
}
</script>
@endpush

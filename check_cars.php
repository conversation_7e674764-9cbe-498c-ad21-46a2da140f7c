<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Car;

echo "Checking cars in database...\n";

$totalCars = Car::count();
echo "Total cars: {$totalCars}\n";

$publicCars = Car::where('public_listing', true)->count();
echo "Public cars: {$publicCars}\n";

if ($publicCars > 0) {
    echo "\nFirst 5 public cars:\n";
    $cars = Car::where('public_listing', true)->take(5)->get(['id', 'make', 'model', 'year']);
    foreach ($cars as $car) {
        echo "ID: {$car->id} - {$car->year} {$car->make} {$car->model}\n";
    }
} else {
    echo "\nNo public cars found. Let's check all cars:\n";
    $cars = Car::take(5)->get(['id', 'make', 'model', 'year', 'public_listing']);
    foreach ($cars as $car) {
        $public = $car->public_listing ? 'YES' : 'NO';
        echo "ID: {$car->id} - {$car->year} {$car->make} {$car->model} (Public: {$public})\n";
    }
}

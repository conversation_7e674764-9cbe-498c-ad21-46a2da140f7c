@extends('public.layouts.app')

@section('title', 'Advanced Search - I-fixit Cars')
@section('meta_description', 'Find your perfect car investment opportunity with our advanced search filters.')

@section('content')
<div class="bg-gray-50 min-h-screen py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Advanced Car Search</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Use our comprehensive search filters to find the perfect car investment opportunity.
            </p>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <form action="{{ route('public.search.perform') }}" method="POST" class="space-y-6">
                @csrf
                
                <!-- Basic Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Keywords</label>
                    <input type="text" id="search" name="search" 
                           value="{{ old('search') }}"
                           placeholder="Search by make, model, year, or features..."
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <!-- Make and Model -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="make" class="block text-sm font-medium text-gray-700 mb-2">Make</label>
                        <select id="make" name="make" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Make</option>
                            <option value="BMW" {{ old('make') == 'BMW' ? 'selected' : '' }}>BMW</option>
                            <option value="Mercedes-Benz" {{ old('make') == 'Mercedes-Benz' ? 'selected' : '' }}>Mercedes-Benz</option>
                            <option value="Audi" {{ old('make') == 'Audi' ? 'selected' : '' }}>Audi</option>
                            <option value="Toyota" {{ old('make') == 'Toyota' ? 'selected' : '' }}>Toyota</option>
                            <option value="Volkswagen" {{ old('make') == 'Volkswagen' ? 'selected' : '' }}>Volkswagen</option>
                            <option value="Ford" {{ old('make') == 'Ford' ? 'selected' : '' }}>Ford</option>
                            <option value="Honda" {{ old('make') == 'Honda' ? 'selected' : '' }}>Honda</option>
                            <option value="Nissan" {{ old('make') == 'Nissan' ? 'selected' : '' }}>Nissan</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="model" class="block text-sm font-medium text-gray-700 mb-2">Model</label>
                        <input type="text" id="model" name="model" 
                               value="{{ old('model') }}"
                               placeholder="Enter model name..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <!-- Year Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="year_from" class="block text-sm font-medium text-gray-700 mb-2">Year From</label>
                        <select id="year_from" name="year_from" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Year</option>
                            @for($year = date('Y'); $year >= 2000; $year--)
                                <option value="{{ $year }}" {{ old('year_from') == $year ? 'selected' : '' }}>{{ $year }}</option>
                            @endfor
                        </select>
                    </div>
                    
                    <div>
                        <label for="year_to" class="block text-sm font-medium text-gray-700 mb-2">Year To</label>
                        <select id="year_to" name="year_to" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Year</option>
                            @for($year = date('Y'); $year >= 2000; $year--)
                                <option value="{{ $year }}" {{ old('year_to') == $year ? 'selected' : '' }}>{{ $year }}</option>
                            @endfor
                        </select>
                    </div>
                </div>

                <!-- Price Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="price_from" class="block text-sm font-medium text-gray-700 mb-2">Price From (R)</label>
                        <input type="number" id="price_from" name="price_from" 
                               value="{{ old('price_from') }}"
                               placeholder="0"
                               min="0"
                               step="1000"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="price_to" class="block text-sm font-medium text-gray-700 mb-2">Price To (R)</label>
                        <input type="number" id="price_to" name="price_to" 
                               value="{{ old('price_to') }}"
                               placeholder="1000000"
                               min="0"
                               step="1000"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <!-- Mileage and Fuel Type -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="mileage_max" class="block text-sm font-medium text-gray-700 mb-2">Maximum Mileage (km)</label>
                        <input type="number" id="mileage_max" name="mileage_max" 
                               value="{{ old('mileage_max') }}"
                               placeholder="200000"
                               min="0"
                               step="1000"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="fuel_type" class="block text-sm font-medium text-gray-700 mb-2">Fuel Type</label>
                        <select id="fuel_type" name="fuel_type" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Fuel Type</option>
                            <option value="petrol" {{ old('fuel_type') == 'petrol' ? 'selected' : '' }}>Petrol</option>
                            <option value="diesel" {{ old('fuel_type') == 'diesel' ? 'selected' : '' }}>Diesel</option>
                            <option value="hybrid" {{ old('fuel_type') == 'hybrid' ? 'selected' : '' }}>Hybrid</option>
                            <option value="electric" {{ old('fuel_type') == 'electric' ? 'selected' : '' }}>Electric</option>
                        </select>
                    </div>
                </div>

                <!-- Transmission and Body Type -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="transmission" class="block text-sm font-medium text-gray-700 mb-2">Transmission</label>
                        <select id="transmission" name="transmission" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Transmission</option>
                            <option value="manual" {{ old('transmission') == 'manual' ? 'selected' : '' }}>Manual</option>
                            <option value="automatic" {{ old('transmission') == 'automatic' ? 'selected' : '' }}>Automatic</option>
                            <option value="semi-automatic" {{ old('transmission') == 'semi-automatic' ? 'selected' : '' }}>Semi-Automatic</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="body_type" class="block text-sm font-medium text-gray-700 mb-2">Body Type</label>
                        <select id="body_type" name="body_type" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Body Type</option>
                            <option value="sedan" {{ old('body_type') == 'sedan' ? 'selected' : '' }}>Sedan</option>
                            <option value="hatchback" {{ old('body_type') == 'hatchback' ? 'selected' : '' }}>Hatchback</option>
                            <option value="suv" {{ old('body_type') == 'suv' ? 'selected' : '' }}>SUV</option>
                            <option value="coupe" {{ old('body_type') == 'coupe' ? 'selected' : '' }}>Coupe</option>
                            <option value="convertible" {{ old('body_type') == 'convertible' ? 'selected' : '' }}>Convertible</option>
                            <option value="wagon" {{ old('body_type') == 'wagon' ? 'selected' : '' }}>Wagon</option>
                            <option value="pickup" {{ old('body_type') == 'pickup' ? 'selected' : '' }}>Pickup</option>
                        </select>
                    </div>
                </div>

                <!-- Investment Options -->
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Investment Criteria</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="investment_potential" value="1" 
                                       {{ old('investment_potential') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Show only cars with investment potential</span>
                            </label>
                        </div>
                        
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="featured_only" value="1" 
                                       {{ old('featured_only') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Featured cars only</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Sort Options -->
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">Sort Results By</label>
                    <select id="sort" name="sort" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="priority" {{ old('sort') == 'priority' ? 'selected' : '' }}>Relevance</option>
                        <option value="price_low" {{ old('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ old('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="year_new" {{ old('sort') == 'year_new' ? 'selected' : '' }}>Year: Newest First</option>
                        <option value="year_old" {{ old('sort') == 'year_old' ? 'selected' : '' }}>Year: Oldest First</option>
                        <option value="mileage_low" {{ old('sort') == 'mileage_low' ? 'selected' : '' }}>Mileage: Low to High</option>
                        <option value="recent" {{ old('sort') == 'recent' ? 'selected' : '' }}>Recently Added</option>
                        <option value="popular" {{ old('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                    </select>
                </div>

                <!-- Submit Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 pt-6">
                    <button type="submit" 
                            class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Search Cars
                    </button>
                    
                    <button type="button" onclick="clearForm()" 
                            class="flex-1 bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-200 transition-colors">
                        <i class="fas fa-undo mr-2"></i>
                        Clear Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Quick Search Presets -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-star text-yellow-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Luxury Cars</h3>
                <p class="text-gray-600 mb-4">Premium vehicles with high investment potential</p>
                <a href="{{ route('public.cars.index', ['make' => 'BMW,Mercedes-Benz,Audi', 'price_from' => 500000]) }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    View Luxury Cars
                </a>
            </div>

            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-chart-line text-green-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Investment Opportunities</h3>
                <p class="text-gray-600 mb-4">Cars with proven ROI potential</p>
                <a href="{{ route('public.cars.index', ['investment_potential' => 1]) }}" 
                   class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    View Investments
                </a>
            </div>

            <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-clock text-blue-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Recent Arrivals</h3>
                <p class="text-gray-600 mb-4">Latest additions to our inventory</p>
                <a href="{{ route('public.cars.index', ['sort' => 'recent']) }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    View Recent
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function clearForm() {
    document.querySelector('form').reset();
}
</script>
@endsection

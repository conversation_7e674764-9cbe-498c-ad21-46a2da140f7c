<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PublicInquiry extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'car_id',
        'name',
        'email',
        'phone',
        'message',
        'inquiry_type',
        'status',
        'additional_data',
        'contacted_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'additional_data' => 'array',
        'contacted_at' => 'datetime',
    ];

    /**
     * Get the car that this inquiry is about.
     */
    public function car()
    {
        return $this->belongsTo(Car::class);
    }

    /**
     * Scope to get new inquiries
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope to get inquiries by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('inquiry_type', $type);
    }

    /**
     * Scope to get recent inquiries
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Mark inquiry as contacted
     */
    public function markAsContacted()
    {
        $this->update([
            'status' => 'contacted',
            'contacted_at' => now(),
        ]);
    }

    /**
     * Get the inquiry type label
     */
    public function getInquiryTypeLabelAttribute()
    {
        return match($this->inquiry_type) {
            'general' => 'General Inquiry',
            'test_drive' => 'Test Drive Request',
            'financing' => 'Financing Inquiry',
            'trade_in' => 'Trade-in Evaluation',
            'investment' => 'Investment Inquiry',
            default => ucfirst($this->inquiry_type),
        };
    }

    /**
     * Get the status label
     */
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'new' => 'New',
            'contacted' => 'Contacted',
            'qualified' => 'Qualified Lead',
            'closed' => 'Closed',
            default => ucfirst($this->status),
        };
    }
}

import { SvgIcon, SvgIconProps } from '@mui/material';

const Icon2 = (props: SvgIconProps) => {
  return (
    <SvgIcon width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
      <path
        d="M4.58317 14.5837H3.33317V5.41699H9.83317L9.1665 6.08366C8.99984 6.25033 8.9165 6.41699 8.9165 6.66699C8.9165 7.16699 9.24984 7.50033 9.74984 7.50033C9.99984 7.50033 10.1665 7.41699 10.3332 7.25033L12.4165 5.16699C12.7498 4.83366 12.7498 4.33366 12.4165 4.00033L10.3332 1.91699C9.99984 1.58366 9.49984 1.58366 9.1665 1.91699C8.83317 2.25033 8.83317 2.75033 9.1665 3.08366L9.83317 3.75033H2.49984C1.99984 3.75033 1.6665 4.08366 1.6665 4.58366V15.417C1.6665 15.917 1.99984 16.2503 2.49984 16.2503H4.58317C5.08317 16.2503 5.4165 15.917 5.4165 15.417C5.4165 14.917 5.08317 14.5837 4.58317 14.5837ZM17.4998 3.75033H15.4165C14.9165 3.75033 14.5832 4.08366 14.5832 4.58366C14.5832 5.08366 14.9165 5.41699 15.4165 5.41699H16.6665V14.5837H9.6665L10.3332 13.917C10.6665 13.5837 10.6665 13.0837 10.3332 12.7503C9.99984 12.417 9.49984 12.417 9.1665 12.7503L7.08317 14.8337C6.74984 15.167 6.74984 15.667 7.08317 16.0003L9.1665 18.0837C9.33317 18.2503 9.49984 18.3337 9.74984 18.3337C9.99984 18.3337 10.1665 18.2503 10.3332 18.0837C10.6665 17.7503 10.6665 17.2503 10.3332 16.917L9.6665 16.2503H17.4998C17.9998 16.2503 18.3332 15.917 18.3332 15.417V4.58366C18.3332 4.08366 17.9998 3.75033 17.4998 3.75033Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default Icon2;

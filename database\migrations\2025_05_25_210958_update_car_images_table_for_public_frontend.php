<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('car_images', function (Blueprint $table) {
            // Add new columns for public frontend
            $table->integer('sort_order')->default(0)->after('image_type');
            $table->string('alt_text')->nullable()->after('sort_order');
            $table->boolean('is_primary')->default(false)->after('alt_text');
            $table->string('caption')->nullable()->after('is_primary');

            // Update image_type enum to include public types
            $table->dropColumn('image_type');
        });

        // Add the new enum column
        Schema::table('car_images', function (Blueprint $table) {
            $table->enum('image_type', ['before_repair', 'during_repair', 'after_repair', 'damage', 'exterior', 'interior', 'engine', 'documents', 'other'])
                  ->default('exterior')
                  ->after('image_path');
        });

        // Add new indexes
        Schema::table('car_images', function (Blueprint $table) {
            $table->index(['car_id', 'image_type']);
            $table->index(['car_id', 'is_primary']);
            $table->index(['car_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('car_images', function (Blueprint $table) {
            // Drop new indexes
            $table->dropIndex(['car_id', 'image_type']);
            $table->dropIndex(['car_id', 'is_primary']);
            $table->dropIndex(['car_id', 'sort_order']);

            // Drop new columns
            $table->dropColumn(['sort_order', 'alt_text', 'is_primary', 'caption']);

            // Revert image_type enum
            $table->dropColumn('image_type');
        });

        Schema::table('car_images', function (Blueprint $table) {
            $table->enum('image_type', ['before_repair', 'during_repair', 'after_repair', 'damage', 'other'])
                  ->after('image_path');
        });
    }
};

import { SvgIcon, SvgIconProps } from '@mui/material';

const ShoppingCart = (props: SvgIconProps) => {
  return (
    <SvgIcon width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.3335 16.667C8.3335 18.0477 7.21421 19.167 5.8335 19.167C4.45278 19.167 3.3335 18.0477 3.3335 16.667C3.3335 15.2863 4.45278 14.167 5.8335 14.167C7.21421 14.167 8.3335 15.2863 8.3335 16.667ZM6.66683 16.667C6.66683 17.1272 6.29373 17.5003 5.8335 17.5003C5.37326 17.5003 5.00016 17.1272 5.00016 16.667C5.00016 16.2068 5.37326 15.8337 5.8335 15.8337C6.29373 15.8337 6.66683 16.2068 6.66683 16.667Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.6668 16.667C16.6668 18.0477 15.5475 19.167 14.1668 19.167C12.7861 19.167 11.6668 18.0477 11.6668 16.667C11.6668 15.2863 12.7861 14.167 14.1668 14.167C15.5475 14.167 16.6668 15.2863 16.6668 16.667ZM15.0002 16.667C15.0002 17.1272 14.6271 17.5003 14.1668 17.5003C13.7066 17.5003 13.3335 17.1272 13.3335 16.667C13.3335 16.2068 13.7066 15.8337 14.1668 15.8337C14.6271 15.8337 15.0002 16.2068 15.0002 16.667Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.75466 2.12685C1.96049 1.7152 2.46105 1.54835 2.8727 1.75418L3.70895 2.1723C4.48334 2.5595 5.00227 3.3198 5.08065 4.18203L5.12052 4.62059C5.14003 4.8352 5.31998 4.99953 5.53548 4.99953H16.5296C17.9842 4.99953 18.991 6.45239 18.4803 7.81437L17.0192 11.7107C16.6533 12.6864 15.7205 13.3329 14.6784 13.3329H6.52204C5.22904 13.3329 4.14937 12.3469 4.03231 11.0592L3.42083 4.33293C3.3947 4.04551 3.22173 3.79208 2.9636 3.66302L2.12734 3.24489C1.71569 3.03906 1.54884 2.5385 1.75466 2.12685ZM5.76275 6.6662C5.51774 6.6662 5.32561 6.87658 5.34779 7.12059L5.69213 10.9083C5.73115 11.3375 6.09104 11.6662 6.52204 11.6662H14.6784C15.0257 11.6662 15.3367 11.4507 15.4586 11.1255L16.9197 7.22917C17.0219 6.95677 16.8205 6.6662 16.5296 6.6662H5.76275Z"
        fill="currentColor"
      />
    </SvgIcon>
  );
};

export default ShoppingCart;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('public_inquiries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('car_id')->constrained()->onDelete('cascade');
            $table->string('name', 100);
            $table->string('email');
            $table->string('phone', 20)->nullable();
            $table->text('message');
            $table->enum('inquiry_type', ['general', 'test_drive', 'financing', 'trade_in', 'investment'])->default('general');
            $table->enum('status', ['new', 'contacted', 'qualified', 'closed'])->default('new');
            $table->json('additional_data')->nullable(); // For storing extra form data
            $table->timestamp('contacted_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['inquiry_type', 'status']);
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('public_inquiries');
    }
};

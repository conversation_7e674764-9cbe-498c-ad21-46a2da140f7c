<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Car;
use App\Models\CarImage;
use App\Models\CarFeature;

echo "Updating cars for public display...\n";

// Get all public cars
$cars = Car::where('public_listing', true)->get();

foreach ($cars as $car) {
    echo "Updating car: {$car->make} {$car->model}\n";
    
    // Update car details
    $car->update([
        'estimated_market_value' => $car->purchase_price * 1.15,
        'public_description' => 'This premium vehicle offers excellent investment potential with proven reliability and strong resale value. Carefully inspected and maintained to the highest standards.',
        'meta_title' => $car->year . ' ' . $car->make . ' ' . $car->model . ' - Premium Investment Opportunity',
        'meta_description' => 'Excellent condition ' . $car->make . ' ' . $car->model . ' with strong investment potential and proven reliability.',
    ]);
    
    // Create sample images if they don't exist
    if ($car->carImages()->count() === 0) {
        echo "Adding images for {$car->make} {$car->model}\n";
        
        // Try to create with the new structure first
        try {
            CarImage::create([
                'car_id' => $car->id,
                'image_path' => "car_images/sample_{$car->id}_exterior.jpg",
                'image_type' => 'exterior',
                'sort_order' => 0,
                'alt_text' => "{$car->year} {$car->make} {$car->model} exterior view",
                'is_primary' => true,
                'caption' => "Exterior view of {$car->year} {$car->make} {$car->model}",
            ]);
        } catch (Exception $e) {
            // Fallback to old structure
            echo "Using fallback image structure\n";
            CarImage::create([
                'car_id' => $car->id,
                'image_path' => "car_images/sample_{$car->id}_exterior.jpg",
                'image_type' => 'other',
                'description' => "Exterior view of {$car->year} {$car->make} {$car->model}",
            ]);
        }
    }
    
    // Attach features if none exist
    if ($car->features()->count() === 0) {
        $features = CarFeature::inRandomOrder()->limit(rand(5, 8))->get();
        if ($features->count() > 0) {
            $car->features()->attach($features->pluck('id'));
            echo "Added features for {$car->make} {$car->model}\n";
        }
    }
}

echo "Updated " . $cars->count() . " cars successfully!\n";
echo "Total public cars: " . Car::where('public_listing', true)->count() . "\n";
echo "Total featured cars: " . Car::where('featured', true)->count() . "\n";

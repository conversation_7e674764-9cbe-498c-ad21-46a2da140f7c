<?php

namespace App\Providers;

use App\Models\Contact;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class ViewComposerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share unread contact messages count with navigation views
        View::composer(['layouts.navigation'], function ($view) {
            $unreadContactCount = 0;

            // Skip database queries during testing to avoid connection issues
            if (!app()->environment('testing') && Auth::check() && Auth::user()->hasAdminAccess()) {
                try {
                    $unreadContactCount = Contact::where('is_read', false)->count();
                } catch (\Exception $e) {
                    // Gracefully handle database connection issues
                    $unreadContactCount = 0;
                }
            }

            $view->with('unreadContactCount', $unreadContactCount);
        });
    }
}

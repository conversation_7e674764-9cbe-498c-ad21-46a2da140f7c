@php
use Illuminate\Support\Facades\Storage;
@endphp

<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $car->year }} {{ $car->make }} {{ $car->model }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('cars.edit', $car) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    {{ __('Edit Car') }}
                </a>
                <a href="{{ route('cars.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    {{ __('Back to Cars') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Car Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <x-flash-message />

                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Car Details') }}</h3>
                        </div>
                        <div>
                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full
                                @if($car->current_phase == 'bidding') bg-blue-100 text-blue-800
                                @elseif($car->current_phase == 'fixing') bg-yellow-100 text-yellow-800
                                @elseif($car->current_phase == 'dealership') bg-green-100 text-green-800
                                @elseif($car->current_phase == 'sold') bg-purple-100 text-purple-800
                                @endif">
                                {{ ucfirst($car->current_phase) }}
                            </span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Basic Information') }}</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Make:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->make }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Model:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->model }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Variant/Trim:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->variant ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Year:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->year }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('VIN:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->vin ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Registration:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->registration_number ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Color:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->color ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Interior Type:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->interior_type ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Body Type:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->body_type }}</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Technical Details') }}</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Engine Size:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->engine_size ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Fuel Type:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->fuel_type }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Transmission:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->transmission }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Mileage:') }}</span>
                                    <span class="text-sm font-medium">{{ number_format($car->mileage) }} km</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Operational Status:') }}</span>
                                    <span class="text-sm font-medium">{{ ucfirst($car->operational_status) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Damage Severity:') }}</span>
                                    <span class="text-sm font-medium">{{ ucfirst($car->damage_severity) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Vehicle Code:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->vehicle_code ?? 'N/A' }}</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Purchase Information') }}</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Purchase Date:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->purchase_date->format('d M Y') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Purchase Price:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->purchase_price, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Auction House:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->auction_house ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Auction Lot #:') }}</span>
                                    <span class="text-sm font-medium">{{ $car->auction_lot_number ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Transportation Cost:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->transportation_cost, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Registration Papers:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->registration_papers_cost, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Number Plates:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->number_plates_cost, 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Damage Description') }}</h4>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <p class="text-sm text-gray-700">{{ $car->damage_description }}</p>
                        </div>
                    </div>

                    @if($car->notes)
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Notes') }}</h4>
                            <div class="bg-gray-50 p-4 rounded-md">
                                <p class="text-sm text-gray-700">{{ $car->notes }}</p>
                            </div>
                        </div>
                    @endif

                    @if(Auth::user()->role === 'admin' || Auth::id() === $car->created_by)
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Record Information') }}</h4>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <div class="flex items-center mb-2">
                                        <span class="font-medium mr-2">{{ __('Status:') }}</span>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $car->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($car->status) }}
                                        </span>
                                    </div>
                                    @if($car->creator)
                                    <div class="flex items-center mb-2">
                                        <span class="font-medium mr-2">{{ __('Created by:') }}</span>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {{ $car->creator->name }}
                                        </span>
                                    </div>
                                    @endif
                                    <div class="mb-2">
                                        <span class="font-medium mr-2">{{ __('Created at:') }}</span>
                                        {{ $car->created_at->format('M d, Y H:i') }}
                                    </div>
                                </div>
                                <div>
                                    @if($car->updater && $car->updated_at->gt($car->created_at))
                                    <div class="flex items-center mb-2">
                                        <span class="font-medium mr-2">{{ __('Last updated by:') }}</span>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                            {{ $car->updater->name }}
                                        </span>
                                    </div>
                                    <div class="mb-2">
                                        <span class="font-medium mr-2">{{ __('Last updated at:') }}</span>
                                        {{ $car->updated_at->format('M d, Y H:i') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Car Images -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Car Images') }}</h3>
                        <div class="flex space-x-2">
                            @if($car->images->count() > 0)
                            <form action="{{ route('car_images.migrate', $car) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                                    </svg>
                                    {{ __('Organize Images') }}
                                </button>
                            </form>
                            @endif
                            <a href="{{ route('car_images.create', $car) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                {{ __('Add Images') }}
                            </a>
                        </div>
                    </div>

                    @if($car->images->count() > 0)
                        <div class="car-gallery image-gallery grid grid-cols-2 md:grid-cols-4 gap-4">
                            @foreach($car->images as $image)
                                <div class="relative group">
                                    <a href="{{ Storage::disk('public')->url($image->image_path) }}" class="gallery-item"
                                       data-caption="{{ ucfirst(str_replace('_', ' ', $image->image_type)) }}"
                                       data-title="{{ $car->year }} {{ $car->make }} {{ $car->model }} {{ $car->variant ?? '' }}{{ $image->description ? ' - ' . $image->description : '' }}"
                                       data-image-id="{{ $image->id }}"
                                       data-delete-url="{{ route('car_images.destroy', [$car, $image]) }}">
                                        <img src="{{ Storage::disk('public')->url($image->image_path) }}" alt="{{ $image->description }}" class="h-40 w-full object-cover rounded-md">
                                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex flex-col items-center justify-center rounded-md">
                                            <div class="text-white text-center p-2">
                                                <p class="text-sm font-medium">{{ ucfirst($image->image_type) }}</p>
                                                @if($image->description)
                                                    <p class="text-xs">{{ $image->description }}</p>
                                                @endif
                                            </div>
                                            <div class="mt-2">
                                                <form action="{{ route('car_images.destroy', [$car, $image]) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this image?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1 rounded">
                                                        {{ __('Delete') }}
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="py-8 flex flex-col items-center justify-center text-center">
                            <div class="bg-gray-100 p-3 rounded-full mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('No images yet') }}</h4>
                            <p class="text-gray-600 mb-4">{{ __('You haven\'t added any images for this car yet.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Damaged Parts -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Damaged Parts') }}</h3>
                        <a href="{{ route('damaged_parts.create', $car) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            {{ __('Add Damaged Part') }}
                        </a>
                    </div>

                    @if($car->damagedParts->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Part Name') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Location') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Damage Description') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Estimated Cost') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($car->damagedParts as $part)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $part->part_name }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $part->part_location }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-500">{{ Str::limit($part->damage_description, 50) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($part->estimated_repair_cost, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $part->is_repaired ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                    {{ $part->is_repaired ? __('Repaired') : __('Needs Repair') }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('damaged_parts.edit', [$car, $part]) }}" class="text-blue-600 hover:text-blue-900">{{ __('Edit') }}</a>
                                                    <form action="{{ route('damaged_parts.destroy', [$car, $part]) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this damaged part?');">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">{{ __('Delete') }}</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="py-8 flex flex-col items-center justify-center text-center">
                            <div class="bg-gray-100 p-3 rounded-full mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('No damaged parts recorded') }}</h4>
                            <p class="text-gray-600 mb-4">{{ __('You haven\'t added any damaged parts for this car yet.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Parts -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Parts') }}</h3>
                        <a href="{{ route('parts.create', $car) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            {{ __('Add Part') }}
                        </a>
                    </div>

                    @if($car->parts->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Name') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Condition') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Quantity') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Unit Price') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Total Price') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($car->parts as $part)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $part->name }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ ucfirst($part->condition) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $part->quantity }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($part->unit_price, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($part->total_price, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('parts.edit', [$car, $part]) }}" class="text-blue-600 hover:text-blue-900">{{ __('Edit') }}</a>
                                                    <form action="{{ route('parts.destroy', [$car, $part]) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this part?');">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">{{ __('Delete') }}</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="py-8 flex flex-col items-center justify-center text-center">
                            <div class="bg-gray-100 p-3 rounded-full mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('No parts recorded') }}</h4>
                            <p class="text-gray-600 mb-4">{{ __('You haven\'t added any parts for this car yet.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Labor -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Labor') }}</h3>
                        <a href="{{ route('labor.create', $car) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            {{ __('Add Labor') }}
                        </a>
                    </div>

                    @if($car->laborEntries->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Service Type') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Provider') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Hours') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Total Cost') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Service Date') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($car->laborEntries as $labor)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $labor->service_type }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $labor->provider_name }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $labor->hours ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($labor->total_cost, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $labor->service_date->format('d M Y') }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('labor.edit', [$car, $labor]) }}" class="text-blue-600 hover:text-blue-900">{{ __('Edit') }}</a>
                                                    <form action="{{ route('labor.destroy', [$car, $labor]) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this labor entry?');">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">{{ __('Delete') }}</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="py-8 flex flex-col items-center justify-center text-center">
                            <div class="bg-gray-100 p-3 rounded-full mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('No labor entries recorded') }}</h4>
                            <p class="text-gray-600 mb-4">{{ __('You haven\'t added any labor entries for this car yet.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Painting -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Painting') }}</h3>
                        <a href="{{ route('painting.create', $car) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            {{ __('Add Painting') }}
                        </a>
                    </div>

                    @if($car->paintingEntries->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Type') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Provider') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Material Cost') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Labor Cost') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Total Cost') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($car->paintingEntries as $painting)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ ucfirst($painting->painting_type) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $painting->provider_name }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($painting->material_cost ?? 0, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($painting->labor_cost ?? 0, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R {{ number_format($painting->total_cost, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('painting.edit', [$car, $painting]) }}" class="text-blue-600 hover:text-blue-900">{{ __('Edit') }}</a>
                                                    <form action="{{ route('painting.destroy', [$car, $painting]) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this painting entry?');">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">{{ __('Delete') }}</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="py-8 flex flex-col items-center justify-center text-center">
                            <div class="bg-gray-100 p-3 rounded-full mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('No painting entries recorded') }}</h4>
                            <p class="text-gray-600 mb-4">{{ __('You haven\'t added any painting entries for this car yet.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sale Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Sale Information') }}</h3>
                        @if(!$car->sale)
                            <a href="{{ route('sales.create', $car) }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                {{ __('Add Sale Information') }}
                            </a>
                        @else
                            <a href="{{ route('sales.edit', [$car, $car->sale]) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                {{ __('Edit Sale Information') }}
                            </a>
                        @endif
                    </div>

                    @if($car->sale)
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Listing Details') }}</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">{{ __('Listing Date:') }}</span>
                                        <span class="text-sm font-medium">{{ $car->sale->listing_date->format('d M Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">{{ __('Asking Price:') }}</span>
                                        <span class="text-sm font-medium">R {{ number_format($car->sale->asking_price, 2) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">{{ __('Platform:') }}</span>
                                        <span class="text-sm font-medium">{{ $car->sale->platform }}</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Sale Details') }}</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">{{ __('Selling Price:') }}</span>
                                        <span class="text-sm font-medium">{{ $car->sale->selling_price ? 'R ' . number_format($car->sale->selling_price, 2) : 'Not sold yet' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">{{ __('Sale Date:') }}</span>
                                        <span class="text-sm font-medium">{{ $car->sale->sale_date ? $car->sale->sale_date->format('d M Y') : 'Not sold yet' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">{{ __('Buyer:') }}</span>
                                        <span class="text-sm font-medium">{{ $car->sale->buyer_name ?? 'Not sold yet' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($car->sale->notes)
                            <div class="mt-4">
                                <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Sale Notes') }}</h4>
                                <div class="bg-gray-50 p-4 rounded-md">
                                    <p class="text-sm text-gray-700">{{ $car->sale->notes }}</p>
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="py-8 flex flex-col items-center justify-center text-center">
                            <div class="bg-gray-100 p-3 rounded-full mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('No sale information') }}</h4>
                            <p class="text-gray-600 mb-4">{{ __('You haven\'t added any sale information for this car yet.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Financial Summary') }}</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Investment') }}</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Purchase Price:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->purchase_price, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Transportation:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->transportation_cost, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Registration Papers:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->registration_papers_cost, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Number Plates:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->number_plates_cost, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Parts Cost:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->parts->sum('total_price'), 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Labor Cost:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->laborEntries->sum('total_cost'), 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Painting Cost:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->paintingEntries->sum('total_cost'), 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Other Costs:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->other_costs, 2) }}</span>
                                </div>
                                <div class="pt-2 border-t border-gray-200 flex justify-between font-medium">
                                    <span class="text-sm text-gray-900">{{ __('Total Investment:') }}</span>
                                    <span class="text-sm text-gray-900">R {{ number_format($car->getTotalInvestmentAttribute(), 2) }}</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Profit/Loss Projection') }}</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Total Investment:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->getTotalInvestmentAttribute(), 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Estimated Market Value:') }}</span>
                                    <span class="text-sm font-medium">R {{ number_format($car->estimated_market_value ?? 0, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">{{ __('Dealership Discount:') }}</span>
                                    <span class="text-sm font-medium">
                                        R {{ number_format($car->dealership_discount, 2) }}
                                        @if($car->current_phase === 'dealership')
                                            <a href="{{ route('dealership.edit-discount', $car) }}" class="ml-2 text-xs text-blue-600 hover:text-blue-900">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                {{ __('Edit') }}
                                            </a>
                                        @endif
                                    </span>
                                </div>

                                <!-- Projected Profit/Loss (always shown) -->
                                <div class="pt-2 border-t border-gray-200 flex justify-between font-medium">
                                    <span class="text-sm text-gray-900">{{ __('Projected Profit/Loss:') }}</span>
                                    <span class="text-sm {{ $car->getProjectedProfitLossAttribute() >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        R {{ number_format($car->getProjectedProfitLossAttribute(), 2) }}
                                        ({{ number_format($car->getProjectedRoiPercentageAttribute(), 1) }}%)
                                    </span>
                                </div>

                                @if($car->sale && $car->sale->selling_price)
                                    <div class="mt-4 pt-2 border-t border-gray-200">
                                        <h4 class="text-md font-medium text-gray-900 mb-2">{{ __('Actual Sale Results') }}</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-500">{{ __('Actual Selling Price:') }}</span>
                                                <span class="text-sm font-medium">R {{ number_format($car->sale->selling_price, 2) }}</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-500">{{ __('Commission & Fees:') }}</span>
                                                <span class="text-sm font-medium">R {{ number_format(($car->sale->commission + $car->sale->fees), 2) }}</span>
                                            </div>
                                            <div class="pt-2 border-t border-gray-200 flex justify-between font-medium">
                                                <span class="text-sm text-gray-900">{{ __('Actual Profit/Loss:') }}</span>
                                                <span class="text-sm {{ $car->getProfitLossAttribute() >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                    R {{ number_format($car->getProfitLossAttribute(), 2) }}
                                                    ({{ number_format($car->getRoiPercentageAttribute(), 1) }}%)
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

@push('scripts')
<script src="{{ asset('js/car-image-handler.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Lightbox for car images
        const lightbox = new SimpleLightbox('.gallery-item', {
            captionsData: 'caption',
            captionPosition: 'bottom',
            animationSpeed: 200,
            swipeClose: true,
            showCounter: true,
            enableKeyboard: true,
            additionalHtml: function(item) {
                const imageId = item.element.dataset.imageId;
                const deleteUrl = item.element.dataset.deleteUrl;

                if (imageId && deleteUrl) {
                    return `
                        <div class="sl-delete-button">
                            <form action="${deleteUrl}" method="POST" onsubmit="return confirm('Are you sure you want to delete this image?');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded">
                                    Delete Image
                                </button>
                            </form>
                        </div>
                    `;
                }
                return '';
            }
        });
    });
</script>
@endpush
